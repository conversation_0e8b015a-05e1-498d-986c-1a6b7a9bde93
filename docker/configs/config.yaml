# Server Config
server_port: "8080"
worker_count: 20
queue_size: 100
rate_limit_rps: 300

# Code Execute Config
exec_timeout: "90s"
max_code_size: 1048576
memory_limit_mb: 1024

# Resource limits for ulimit wrapper
file_size_limit_kb: 10240
max_processes: 10
max_open_files: 100
max_stack_size_kb: 8192

# Logging configuration
log:
  level: "info"
  file_path: "logs/app.log"
  max_size: 100
  max_age: 30
  max_backups: 10
  compress: true
  console: true
  file_output: true
  async: true
  buffer_size: 256

# Security validation configuration
security:
  # Global security validation switch
  enable: true
  
  # Blocked imports (critical risk - execution will be blocked)
  # Note: urllib and socket moved to network_requests section for conditional control
  blocked_imports: [os, sys, subprocess, ctypes, multiprocessing, threading, importlib, __builtin__, builtins, marshal, pickle, shelve, dbm, tempfile, shutil, glob, platform, pwd, grp]
  
  # Network request control configuration
  network_requests:
    allow: true  # Whether to allow network requests (default: true for backward compatibility)
    # Modules to block when allow=false
    blocked_when_disabled: [urllib, urllib2, urllib3, socket, http.client, httplib, httplib2, requests, httpx, aiohttp, ssl, websocket, websockets, ftplib, smtplib, poplib, imaplib, telnetlib]
    risk_level: "critical"  # Risk level when violated: critical (block execution) | high (warning only)
  
  # Resource limits configuration (replaces hardcoded values)
  resource_limits:
    max_loop_iterations: 1000000      # Maximum iterations in a single loop (default: 1M)
    max_memory_allocation: 100000000  # Maximum elements in memory allocation (default: 100M)
    max_string_repetition: 10000000   # Maximum string/list multiplication factor (default: 10M)
    max_exponentiation: 1000          # Maximum exponent value in power operations
    max_loop_nesting: 3               # Maximum loop nesting depth
    max_comprehension_nesting: 2      # Maximum nested comprehensions
  
  # Validator behavior configuration
  validator:
    early_termination: true   # Stop validation on first critical violation
    max_violations: 50        # Maximum number of violations to record
    validation_timeout: 10    # Timeout for validation in seconds
