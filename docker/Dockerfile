# 多阶段构建
# 第一阶段：构建Go应用
FROM golang:1.25.0-alpine AS builder

# Set working directory
WORKDIR /app

# 复制go mod文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY cmd/ ./cmd/
COPY internal/ ./internal/

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main ./cmd

# 第二阶段：运行时镜像
FROM python:3.13-slim

# 安装系统构建依赖
RUN apt-get update && apt-get install -y \
    ca-certificates \
    curl \
    gcc \
    g++ \
    gfortran \
    libc6-dev \
    libffi-dev \
    libopenblas-dev \
    liblapack-dev \
    pkg-config \
    python3-dev \
    && rm -rf /var/lib/apt/lists/*

# 升级pip和安装构建工具
RUN pip install --no-cache-dir --upgrade pip setuptools wheel

# 复制Python依赖文件
COPY requirements.txt /tmp/requirements.txt

# 安装Python包
RUN pip install --no-cache-dir -r /tmp/requirements.txt \
    && rm /tmp/requirements.txt

# 全面预编译Python标准库和已安装包以优化导入性能
# 编译三个优化级别，使用并行编译加速
RUN echo "Starting comprehensive Python precompilation..." && \
    python3 -m compileall -q -f -j 0 /usr/local/lib/python3.13 && \
    python3 -O -m compileall -q -f -j 0 /usr/local/lib/python3.13 && \
    python3 -OO -m compileall -q -f -j 0 /usr/local/lib/python3.13 && \
    echo "Precompilation completed successfully"

# 验证预编译效果并统计字节码文件数量
RUN python3 -c "import os; import numpy, pandas, matplotlib; \
    pyc_count = sum(1 for root, dirs, files in os.walk('/usr/local/lib/python3.13') \
                    for f in files if f.endswith('.pyc')); \
    print(f'Successfully precompiled {pyc_count} Python bytecode files')"

# 清理构建依赖减少镜像大小（保留运行时需要的库）
RUN apt-get purge -y --auto-remove \
    gcc g++ gfortran libc6-dev libffi-dev python3-dev pkg-config \
    && apt-get autoremove -y \
    && apt-get clean

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set working directory
WORKDIR /app

# Copy Go binary from builder stage
COPY --from=builder /app/main .

# Create scripts directory
RUN mkdir -p /app/scripts /app/logs

# Copy all scripts to scripts directory
COPY scripts/ /app/scripts/

# Change ownership and make scripts executable
RUN chown -R appuser:appuser /app && \
    chmod +x /app/scripts/startup.sh && \
    chmod +x /app/scripts/python-limited.sh

# Create and configure temp directory for Python execution isolation
# Set sticky bit to prevent users from deleting others' temp files
RUN mkdir -p /tmp && chmod 1777 /tmp

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8080

# Set timezone and config path
ENV TZ=Asia/Shanghai \
    PYTHONOPTIMIZE=1 \
    PYTHONUNBUFFERED=1 \
    CONFIG_PATH=/app/config.yaml

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Start application with warmup
CMD ["/app/scripts/startup.sh"]