x-code-sandbox-common: &code-sandbox-common
  build:
    context: ..
    dockerfile: docker/Dockerfile
  image: code-sandbox:latest
  environment:
    - TZ=Asia/Shanghai  # Set timezone to Shanghai
    - AUTH_TOKEN=${AUTH_TOKEN}  # Load from .env file
    # Optional overrides for other config values
    # - WORKER_COUNT=${WORKER_COUNT}
    # - RATE_LIMIT_RPS=${RATE_LIMIT_RPS}
  networks:
    - code-sandbox-net
  restart: unless-stopped
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 40s
  deploy:
    resources:
      limits:
        cpus: '0.5'
        memory: 4096M
      reservations:
        cpus: '0.25'
        memory: 256M

services:
  code-sandbox-8080:
    <<: *code-sandbox-common
    container_name: code-sandbox-8080
    ports:
      - "8080:8080"
    volumes:
      - ./logs/8080:/app/logs
      - ./configs/config.yaml:/app/config.yaml:ro

  code-sandbox-8081:
    <<: *code-sandbox-common
    container_name: code-sandbox-8081
    ports:
      - "8081:8080"
    volumes:
      - ./logs/8081:/app/logs
      - ./configs/config.yaml:/app/config.yaml:ro

networks:
  code-sandbox-net:
    driver: bridge
