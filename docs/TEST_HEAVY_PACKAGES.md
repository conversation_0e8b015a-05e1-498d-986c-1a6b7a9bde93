# Heavy Package Timeout Testing Guide

## 🎯 Test Objective
Verify that the 300s timeout configuration allows heavy packages (pandas, numpy) to load successfully without timing out.

## 🔧 Test Setup

### 1. Build and Deploy Updated Service
```bash
# Build the updated Docker image
make docker-build

# Deploy with new configuration  
make docker-compose-up

# Verify service is running
curl http://localhost:8080/health
```

### 2. Test Environment Verification
```bash
# Check configuration is applied
curl http://localhost:8080/status
# Should show EXEC_TIMEOUT: 300s
```

## 📊 Test Cases

### Test Case 1: Basic Heavy Package Import
**Purpose**: Verify pandas/numpy can import within 300s timeout

**Test Code** (Base64 encode this):
```python
import time
start_time = time.time()

# Heavy package imports
import pandas as pd
import numpy as np

import_time = time.time() - start_time
print(f"Import time: {import_time:.2f} seconds")

# Simple operations to verify functionality  
df = pd.DataFrame({'x': [1, 2, 3], 'y': [4, 5, 6]})
arr = np.array([1, 2, 3, 4, 5])

print(f"DataFrame shape: {df.shape}")
print(f"Array mean: {np.mean(arr)}")
print("Heavy packages loaded successfully!")
```

**Expected Results**:
- ✅ Execution completes within 300s
- ✅ Response includes warning about heavy packages
- ✅ Import time < 300s (likely 30-120s on first run)
- ✅ `exec_time_ms` shows actual execution time
- ✅ `warning` field provides optimization tips

### Test Case 2: Multiple Heavy Packages
**Purpose**: Test combined heavy package loading

**Test Code**:
```python
import time
start_time = time.time()

# Import multiple heavy packages
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import scipy.stats as stats

import_time = time.time() - start_time
print(f"Multiple package import time: {import_time:.2f} seconds")

# Basic operations
data = np.random.normal(0, 1, 1000)
df = pd.DataFrame({'values': data})
mean_val = stats.describe(data).mean

print(f"Data analysis complete. Mean: {mean_val:.3f}")
```

**Expected Results**:
- ✅ Completes within 300s
- ✅ Warning includes multiple detected packages
- ⚠️ Import time may be 60-180s for all packages

### Test Case 3: Timeout Boundary Testing  
**Purpose**: Verify service handles long-running operations correctly

**Test Code**:
```python
import pandas as pd
import numpy as np
import time

# Simulate heavy computation after import
large_data = np.random.random((10000, 100))
df = pd.DataFrame(large_data)

# Perform time-consuming operations
result = df.groupby(df.index // 1000).mean().std().sum()

print(f"Heavy computation result: {result:.6f}")
print("Long operation completed successfully")
```

### Test Case 4: Warning System Verification
**Purpose**: Verify API warning generation works correctly

**Test Code**:
```python
# Simple operation with heavy package
import pandas as pd

# Quick operation that should trigger package warning but not time warning
df = pd.DataFrame({'a': [1, 2], 'b': [3, 4]})
print(f"Result: {df.sum().sum()}")
```

**Expected Response**:
```json
{
  "id": "uuid-here",
  "success": true,
  "exec_time_ms": 45000,
  "output": "Result: 10\n",
  "warning": "💡 Heavy packages detected: pandas. First import may take longer (45s). Subsequent runs will be faster within the same session."
}
```

## 📋 Test Execution Commands

### Using curl:
```bash
# Encode test code to Base64
TEST_CODE=$(echo 'import pandas as pd
import numpy as np
print("Heavy packages loaded!")' | base64 -w 0)

# Send test request
curl -X POST http://localhost:8080/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-secret-token-here" \
  -d "{\"code_base64\":\"$TEST_CODE\"}" \
  | jq '.'
```

### Using the integrated test suite:
```bash
cd test/
go run integration_test.go -test packages -url http://localhost:8080 -token your-secret-token-here
```

## ✅ Success Criteria

### For Test Case 1 (Basic Import):
- [ ] HTTP 200 response 
- [ ] `success: true`
- [ ] `exec_time_ms` < 300,000 (300 seconds)
- [ ] `warning` field contains "Heavy packages detected: pandas, numpy"
- [ ] Output contains "Heavy packages loaded successfully!"

### For Test Case 2 (Multiple Packages):
- [ ] Completes without timeout
- [ ] Warning includes all detected heavy packages
- [ ] Import time logged in output

### For Test Case 3 (Boundary):
- [ ] Long computation completes successfully
- [ ] No timeout errors
- [ ] Performance warning if execution > 60s

### For Test Case 4 (Warning System):
- [ ] Warning field is populated correctly
- [ ] Warning format matches expected pattern
- [ ] Execution time accurately reported

## 🚨 Failure Scenarios

### If Tests Still Timeout:
1. **Check Docker Resources**: Ensure adequate CPU/memory allocation
2. **Verify Configuration**: Confirm EXEC_TIMEOUT=300s in environment
3. **Container Performance**: Check if container is resource-constrained
4. **Consider Further Timeout Increase**: May need 420s (7 minutes) for very slow environments

### If Warnings Don't Appear:
1. **Check Response JSON**: Ensure warning field is included
2. **Verify Regex Patterns**: Check if import detection works
3. **Test with Different Code**: Try various import styles

## 📊 Expected Performance Baseline

| Package Combination | Expected Import Time | Memory Usage |
|-------------------|---------------------|--------------|
| `pandas` only | 30-60s | 150-200MB |
| `numpy` only | 15-30s | 100-150MB |  
| `pandas + numpy` | 45-90s | 200-250MB |
| `pandas + numpy + matplotlib` | 60-120s | 250-300MB |
| All heavy packages | 90-180s | 300+ MB |

## 🎉 Post-Test Actions

If all tests pass:
- [ ] Document successful timeout configuration 
- [ ] Update service documentation with verified performance characteristics
- [ ] Consider performance optimizations for future versions

If tests fail:
- [ ] Investigate root causes
- [ ] Consider alternative solutions (process pool, etc.)
- [ ] Document limitations and workarounds