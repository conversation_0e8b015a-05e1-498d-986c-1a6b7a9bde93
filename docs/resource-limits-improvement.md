# 资源限制完整性改进方案

## 📌 当前实现的严重缺陷

当前代码仅使用 `ulimit -v` 限制虚拟内存：
```go
// 当前实现 - executor.go:41
cmd := exec.CommandContext(ctx, "bash", "-c", fmt.Sprintf("ulimit -v %d; python3", memLimitKB))
```

### 存在的问题

| 资源类型 | 当前状态 | 风险等级 | 潜在攻击 |
|---------|---------|---------|----------|
| **CPU** | ❌ 无限制 | 🔴 高 | 占用100% CPU，DoS攻击 |
| **物理内存** | ⚠️ 仅限虚拟内存 | 🔴 高 | 内存耗尽，OOM killer |
| **磁盘I/O** | ❌ 无限制 | 🟡 中 | 磁盘写满，I/O阻塞 |
| **网络** | ❌ 无限制 | 🔴 高 | 网络攻击，数据泄露 |
| **进程数** | ❌ 无限制 | 🔴 高 | Fork炸弹 |
| **文件描述符** | ❌ 无限制 | 🟡 中 | 资源耗尽 |
| **系统调用** | ❌ 无过滤 | 🔴 高 | 任意系统调用 |

## 🔧 Linux资源管理机制层次

### 1. 传统ulimit（当前使用）
```bash
# 当前仅使用
ulimit -v 262144  # 虚拟内存 256MB

# 应该使用的完整限制
ulimit -v 262144  # 虚拟内存
ulimit -m 262144  # 物理内存（现代Linux可能不生效）
ulimit -t 30      # CPU时间（秒）
ulimit -u 50      # 最大进程数
ulimit -n 100     # 文件描述符
ulimit -f 10240   # 文件大小（KB）
```

**局限性**：
- 粒度粗，基于进程级别
- 易被绕过（子进程可重置）
- 不支持现代资源类型（网络、I/O带宽）

### 2. cgroups v2（推荐）
Linux内核的资源管理框架，支持：
- **CPU**：份额、配额、核心绑定
- **内存**：物理内存、swap、内核内存
- **I/O**：带宽、IOPS、权重
- **进程数**：最大进程/线程数
- **网络**：分类、优先级（需配合tc）

### 3. Linux Namespaces
提供资源隔离（非限制）：
- **PID namespace**：进程ID隔离
- **Network namespace**：网络栈隔离
- **Mount namespace**：文件系统隔离
- **IPC namespace**：进程间通信隔离
- **UTS namespace**：主机名隔离
- **User namespace**：用户ID隔离

### 4. 容器运行时
- **Docker/containerd**：标准容器
- **gVisor**：用户态内核
- **Firecracker**：microVM
- **Kata Containers**：轻量级VM

## 🚀 三层改进方案

## 方案一：短期快速修复（1周内实现）

### 增强ulimit + 直接使用cgroups v2

#### 1. cgroups v2管理器实现
```go
// pkg/sandbox/cgroup_manager.go
package sandbox

import (
    "fmt"
    "os"
    "path/filepath"
    "strconv"
    "strings"
)

// ResourceLimits 定义资源限制
type ResourceLimits struct {
    // CPU限制
    CPUQuotaPercent int    // CPU使用率百分比 (1-100)
    CPUCores        string // CPU核心绑定 (如 "0-1")
    
    // 内存限制
    MemoryMB     int  // 物理内存限制(MB)
    MemorySwapMB int  // Swap限制(MB)
    
    // I/O限制
    IOReadBps   int64 // 读取速率(bytes/s)
    IOWriteBps  int64 // 写入速率(bytes/s)
    IOReadIOPS  int   // 读IOPS
    IOWriteIOPS int   // 写IOPS
    
    // 进程限制
    PidsMax int // 最大进程数
    
    // 文件系统限制
    FDLimit      int // 文件描述符限制
    FileSizeMB   int // 单文件大小限制
    TmpfsSizeMB  int // 临时文件系统大小
}

// CgroupV2Manager cgroups v2管理器
type CgroupV2Manager struct {
    cgroupPath string
    limits     ResourceLimits
    execID     string
}

// NewCgroupV2Manager 创建新的cgroup管理器
func NewCgroupV2Manager(execID string, limits ResourceLimits) (*CgroupV2Manager, error) {
    // 检查cgroups v2是否可用
    if !isCgroupV2Available() {
        return nil, fmt.Errorf("cgroups v2 not available")
    }
    
    cgroupPath := filepath.Join("/sys/fs/cgroup", "codeexec", execID)
    
    // 创建cgroup目录
    if err := os.MkdirAll(cgroupPath, 0755); err != nil {
        return nil, fmt.Errorf("failed to create cgroup: %w", err)
    }
    
    // 启用需要的控制器
    if err := enableControllers(cgroupPath); err != nil {
        os.RemoveAll(cgroupPath)
        return nil, fmt.Errorf("failed to enable controllers: %w", err)
    }
    
    manager := &CgroupV2Manager{
        cgroupPath: cgroupPath,
        limits:     limits,
        execID:     execID,
    }
    
    // 应用资源限制
    if err := manager.applyLimits(); err != nil {
        manager.Cleanup()
        return nil, fmt.Errorf("failed to apply limits: %w", err)
    }
    
    return manager, nil
}

func (m *CgroupV2Manager) applyLimits() error {
    // CPU限制
    if err := m.setCPULimits(); err != nil {
        return err
    }
    
    // 内存限制
    if err := m.setMemoryLimits(); err != nil {
        return err
    }
    
    // I/O限制
    if err := m.setIOLimits(); err != nil {
        return err
    }
    
    // 进程数限制
    if err := m.setPidsLimit(); err != nil {
        return err
    }
    
    return nil
}

func (m *CgroupV2Manager) setCPULimits() error {
    // CPU配额限制 (cpu.max)
    // 格式: "quota period" 或 "max period"
    // 例如: "50000 100000" 表示50% CPU
    if m.limits.CPUQuotaPercent > 0 {
        quota := m.limits.CPUQuotaPercent * 1000
        period := 100000
        cpuMax := fmt.Sprintf("%d %d", quota, period)
        
        if err := writeFile(m.cgroupPath, "cpu.max", cpuMax); err != nil {
            return fmt.Errorf("failed to set CPU quota: %w", err)
        }
    }
    
    // CPU核心绑定 (cpuset.cpus)
    if m.limits.CPUCores != "" {
        if err := writeFile(m.cgroupPath, "cpuset.cpus", m.limits.CPUCores); err != nil {
            return fmt.Errorf("failed to set CPU cores: %w", err)
        }
    }
    
    return nil
}

func (m *CgroupV2Manager) setMemoryLimits() error {
    // 内存限制 (memory.max)
    if m.limits.MemoryMB > 0 {
        memoryBytes := m.limits.MemoryMB * 1024 * 1024
        if err := writeFile(m.cgroupPath, "memory.max", strconv.Itoa(memoryBytes)); err != nil {
            return fmt.Errorf("failed to set memory limit: %w", err)
        }
    }
    
    // Swap限制 (memory.swap.max)
    if m.limits.MemorySwapMB >= 0 {
        swapBytes := m.limits.MemorySwapMB * 1024 * 1024
        if err := writeFile(m.cgroupPath, "memory.swap.max", strconv.Itoa(swapBytes)); err != nil {
            // Swap可能未启用，记录警告但不失败
            fmt.Printf("Warning: failed to set swap limit: %v\n", err)
        }
    }
    
    // 内存压力通知
    if err := writeFile(m.cgroupPath, "memory.high", 
        strconv.Itoa(int(float64(m.limits.MemoryMB)*0.9*1024*1024))); err != nil {
        fmt.Printf("Warning: failed to set memory.high: %v\n", err)
    }
    
    return nil
}

func (m *CgroupV2Manager) setIOLimits() error {
    // I/O限制需要设备号
    // 获取根设备号（示例：8:0 for /dev/sda）
    deviceID := getDeviceID("/")
    if deviceID == "" {
        return nil // 跳过I/O限制
    }
    
    var ioMax []string
    
    if m.limits.IOReadBps > 0 {
        ioMax = append(ioMax, fmt.Sprintf("%s rbps=%d", deviceID, m.limits.IOReadBps))
    }
    if m.limits.IOWriteBps > 0 {
        ioMax = append(ioMax, fmt.Sprintf("%s wbps=%d", deviceID, m.limits.IOWriteBps))
    }
    if m.limits.IOReadIOPS > 0 {
        ioMax = append(ioMax, fmt.Sprintf("%s riops=%d", deviceID, m.limits.IOReadIOPS))
    }
    if m.limits.IOWriteIOPS > 0 {
        ioMax = append(ioMax, fmt.Sprintf("%s wiops=%d", deviceID, m.limits.IOWriteIOPS))
    }
    
    if len(ioMax) > 0 {
        if err := writeFile(m.cgroupPath, "io.max", strings.Join(ioMax, "\n")); err != nil {
            // I/O限制可能不支持，记录但不失败
            fmt.Printf("Warning: failed to set I/O limits: %v\n", err)
        }
    }
    
    return nil
}

func (m *CgroupV2Manager) setPidsLimit() error {
    if m.limits.PidsMax > 0 {
        if err := writeFile(m.cgroupPath, "pids.max", strconv.Itoa(m.limits.PidsMax)); err != nil {
            return fmt.Errorf("failed to set PIDs limit: %w", err)
        }
    }
    return nil
}

// AddProcess 将进程添加到cgroup
func (m *CgroupV2Manager) AddProcess(pid int) error {
    return writeFile(m.cgroupPath, "cgroup.procs", strconv.Itoa(pid))
}

// GetStats 获取资源使用统计
func (m *CgroupV2Manager) GetStats() (*ResourceStats, error) {
    stats := &ResourceStats{}
    
    // CPU统计
    if data, err := readFile(m.cgroupPath, "cpu.stat"); err == nil {
        stats.CPUStats = parseCPUStats(data)
    }
    
    // 内存统计
    if data, err := readFile(m.cgroupPath, "memory.current"); err == nil {
        stats.MemoryUsageBytes, _ = strconv.ParseInt(strings.TrimSpace(data), 10, 64)
    }
    
    // I/O统计
    if data, err := readFile(m.cgroupPath, "io.stat"); err == nil {
        stats.IOStats = parseIOStats(data)
    }
    
    // 进程数统计
    if data, err := readFile(m.cgroupPath, "pids.current"); err == nil {
        stats.PidsCount, _ = strconv.Atoi(strings.TrimSpace(data))
    }
    
    return stats, nil
}

// Cleanup 清理cgroup
func (m *CgroupV2Manager) Cleanup() error {
    // 等待所有进程退出
    for i := 0; i < 10; i++ {
        procs, _ := readFile(m.cgroupPath, "cgroup.procs")
        if strings.TrimSpace(procs) == "" {
            break
        }
        time.Sleep(100 * time.Millisecond)
    }
    
    return os.RemoveAll(m.cgroupPath)
}

// 辅助函数
func isCgroupV2Available() bool {
    _, err := os.Stat("/sys/fs/cgroup/cgroup.controllers")
    return err == nil
}

func enableControllers(path string) error {
    controllers := []string{"cpu", "memory", "io", "pids"}
    for _, ctrl := range controllers {
        subtreeControl := filepath.Join(filepath.Dir(path), "cgroup.subtree_control")
        if err := writeFile("", subtreeControl, "+"+ctrl); err != nil {
            fmt.Printf("Warning: failed to enable %s controller: %v\n", ctrl, err)
        }
    }
    return nil
}

func writeFile(base, name, content string) error {
    path := filepath.Join(base, name)
    return os.WriteFile(path, []byte(content), 0644)
}

func readFile(base, name string) (string, error) {
    path := filepath.Join(base, name)
    data, err := os.ReadFile(path)
    return string(data), err
}

func getDeviceID(path string) string {
    // 简化实现，实际应该通过stat获取设备号
    return "8:0" // 假设是/dev/sda
}
```

#### 2. 改进的安全执行器
```go
// internal/executor/secure_executor.go
package executor

import (
    "context"
    "fmt"
    "os"
    "os/exec"
    "strings"
    "syscall"
    "time"
    
    "code-sandbox/internal/config"
    "code-sandbox/pkg/sandbox"
)

// SecureExecutor 安全执行器
type SecureExecutor struct {
    config *config.Config
}

// NewSecureExecutor 创建安全执行器
func NewSecureExecutor(cfg *config.Config) *SecureExecutor {
    return &SecureExecutor{
        config: cfg,
    }
}

// Execute 安全执行代码
func (e *SecureExecutor) Execute(job Job) Result {
    startTime := time.Now()
    
    // 1. 代码安全检查
    if err := e.validateCode(job.Code); err != nil {
        return Result{
            ID:      job.ID,
            Success: false,
            Error:   fmt.Sprintf("Code validation failed: %v", err),
        }
    }
    
    // 2. 创建资源限制
    limits := sandbox.ResourceLimits{
        CPUQuotaPercent: 50,                        // 50% CPU
        MemoryMB:        e.config.MemoryLimitMB,    // 内存限制
        MemorySwapMB:    0,                         // 禁用swap
        IOReadBps:       10 * 1024 * 1024,         // 10MB/s读
        IOWriteBps:      10 * 1024 * 1024,         // 10MB/s写
        IOReadIOPS:      100,                       // 100 IOPS读
        IOWriteIOPS:     100,                       // 100 IOPS写
        PidsMax:         50,                        // 最多50个进程
        FDLimit:         100,                       // 100个文件描述符
        FileSizeMB:      10,                        // 单文件10MB
        TmpfsSizeMB:     10,                        // tmpfs 10MB
    }
    
    // 3. 创建cgroup管理器
    cgroupMgr, err := sandbox.NewCgroupV2Manager(job.ID, limits)
    if err != nil {
        // 降级到ulimit限制
        return e.executeWithUlimit(job)
    }
    defer cgroupMgr.Cleanup()
    
    // 4. 准备执行环境
    ctx, cancel := context.WithTimeout(context.Background(), e.config.ExecTimeout)
    defer cancel()
    
    // 5. 创建Python进程
    cmd := exec.CommandContext(ctx, "python3", "-c", job.Code)
    
    // 6. 设置进程属性（namespace隔离）
    cmd.SysProcAttr = &syscall.SysProcAttr{
        Cloneflags: syscall.CLONE_NEWNS |     // Mount namespace
                   syscall.CLONE_NEWPID |      // PID namespace  
                   syscall.CLONE_NEWNET |      // Network namespace
                   syscall.CLONE_NEWIPC |      // IPC namespace
                   syscall.CLONE_NEWUTS,       // UTS namespace
        Pdeathsig:  syscall.SIGKILL,          // 父进程死亡时杀死子进程
        Setpgid:    true,
        Pgid:       0,
    }
    
    // 7. 设置环境变量
    cmd.Env = []string{
        "PATH=/usr/local/bin:/usr/bin:/bin",
        "PYTHONDONTWRITEBYTECODE=1",
        "PYTHONUNBUFFERED=1",
        "HOME=/tmp",
        "TMPDIR=/tmp",
    }
    
    // 8. 设置工作目录
    cmd.Dir = "/tmp"
    
    // 9. 启动进程
    if err := cmd.Start(); err != nil {
        return Result{
            ID:      job.ID,
            Success: false,
            Error:   fmt.Sprintf("Failed to start process: %v", err),
        }
    }
    
    // 10. 将进程加入cgroup
    if err := cgroupMgr.AddProcess(cmd.Process.Pid); err != nil {
        cmd.Process.Kill()
        return Result{
            ID:      job.ID,
            Success: false,
            Error:   fmt.Sprintf("Failed to add process to cgroup: %v", err),
        }
    }
    
    // 11. 设置资源监控
    go e.monitorResources(ctx, cgroupMgr, job.ID)
    
    // 12. 等待执行完成
    output, err := cmd.CombinedOutput()
    
    // 13. 获取资源使用统计
    stats, _ := cgroupMgr.GetStats()
    
    // 14. 构建结果
    result := Result{
        ID:       job.ID,
        Output:   string(output),
        Success:  err == nil,
        ExecTime: time.Since(startTime),
        Stats:    stats,
    }
    
    if err != nil {
        if ctx.Err() == context.DeadlineExceeded {
            result.Error = "Execution timeout"
        } else if exitErr, ok := err.(*exec.ExitError); ok {
            result.Error = fmt.Sprintf("Process exited with code %d", exitErr.ExitCode())
        } else {
            result.Error = err.Error()
        }
    }
    
    return result
}

// validateCode 验证代码安全性
func (e *SecureExecutor) validateCode(code string) error {
    // 危险模式检测
    dangerousPatterns := []string{
        `\bos\.system\b`,
        `\bsubprocess\.(run|call|Popen)\b`,
        `\beval\s*\(`,
        `\bexec\s*\(`,
        `\b__import__\b`,
        `\bopen\s*\([^)]*['"]/`,  // 尝试访问根目录
        `\bsocket\b`,
        `\burllib\b`,
        `\brequests\b`,
    }
    
    for _, pattern := range dangerousPatterns {
        if matched, _ := regexp.MatchString(pattern, code); matched {
            return fmt.Errorf("detected potentially dangerous pattern: %s", pattern)
        }
    }
    
    // 代码长度检查
    if len(code) > e.config.MaxCodeSize {
        return fmt.Errorf("code size exceeds limit: %d > %d", len(code), e.config.MaxCodeSize)
    }
    
    return nil
}

// executeWithUlimit 使用ulimit的降级执行
func (e *SecureExecutor) executeWithUlimit(job Job) Result {
    ctx, cancel := context.WithTimeout(context.Background(), e.config.ExecTimeout)
    defer cancel()
    
    // 构建完整的ulimit命令
    ulimitCmd := fmt.Sprintf(`
        ulimit -v %d;     # 虚拟内存
        ulimit -t %d;     # CPU时间(秒)
        ulimit -u 50;     # 进程数
        ulimit -n 100;    # 文件描述符
        ulimit -f 10240;  # 文件大小(KB)
        python3 -c '%s'
    `, e.config.MemoryLimitMB*1024, int(e.config.ExecTimeout.Seconds()), job.Code)
    
    cmd := exec.CommandContext(ctx, "bash", "-c", ulimitCmd)
    cmd.Env = []string{
        "PATH=/usr/local/bin:/usr/bin:/bin",
        "PYTHONDONTWRITEBYTECODE=1",
    }
    
    output, err := cmd.CombinedOutput()
    
    result := Result{
        ID:      job.ID,
        Output:  string(output),
        Success: err == nil,
    }
    
    if err != nil {
        if ctx.Err() == context.DeadlineExceeded {
            result.Error = "Execution timeout"
        } else {
            result.Error = err.Error()
        }
    }
    
    return result
}

// monitorResources 监控资源使用
func (e *SecureExecutor) monitorResources(ctx context.Context, mgr *sandbox.CgroupV2Manager, execID string) {
    ticker := time.NewTicker(100 * time.Millisecond)
    defer ticker.Stop()
    
    for {
        select {
        case <-ctx.Done():
            return
        case <-ticker.C:
            stats, err := mgr.GetStats()
            if err != nil {
                continue
            }
            
            // 检查是否超过软限制
            if stats.MemoryUsageBytes > int64(e.config.MemoryLimitMB*1024*1024*0.9) {
                fmt.Printf("Warning: Execution %s approaching memory limit: %d MB\n",
                    execID, stats.MemoryUsageBytes/1024/1024)
            }
        }
    }
}
```

## 方案二：中期Docker集成（2-3周）

### 使用Docker SDK实现容器化执行

```go
// pkg/container/docker_executor.go
package container

import (
    "bytes"
    "context"
    "fmt"
    "io"
    "time"
    
    "github.com/docker/docker/api/types"
    "github.com/docker/docker/api/types/container"
    "github.com/docker/docker/api/types/mount"
    "github.com/docker/docker/api/types/network"
    "github.com/docker/docker/client"
    "github.com/docker/go-connections/nat"
)

// DockerExecutor Docker执行器
type DockerExecutor struct {
    client       *client.Client
    image        string
    networkID    string
    volumeID     string
    execTimeout  time.Duration
}

// DockerConfig Docker执行器配置
type DockerConfig struct {
    Image            string
    RegistryAuth     string
    NetworkMode      string
    EnableGPU        bool
    PrewarmPoolSize  int
    MaxContainers    int
}

// NewDockerExecutor 创建Docker执行器
func NewDockerExecutor(cfg DockerConfig) (*DockerExecutor, error) {
    cli, err := client.NewClientWithOpts(
        client.FromEnv,
        client.WithAPIVersionNegotiation(),
    )
    if err != nil {
        return nil, fmt.Errorf("failed to create Docker client: %w", err)
    }
    
    executor := &DockerExecutor{
        client:      cli,
        image:       cfg.Image,
        execTimeout: 30 * time.Second,
    }
    
    // 准备环境
    if err := executor.prepareEnvironment(context.Background()); err != nil {
        return nil, err
    }
    
    return executor, nil
}

// prepareEnvironment 准备Docker环境
func (d *DockerExecutor) prepareEnvironment(ctx context.Context) error {
    // 1. 拉取镜像
    reader, err := d.client.ImagePull(ctx, d.image, types.ImagePullOptions{})
    if err != nil {
        return fmt.Errorf("failed to pull image: %w", err)
    }
    defer reader.Close()
    io.Copy(io.Discard, reader)
    
    // 2. 创建隔离网络
    netResp, err := d.client.NetworkCreate(ctx, "codeexec-net", types.NetworkCreate{
        Driver:     "bridge",
        Internal:   true,  // 内部网络，无外网访问
        Attachable: true,
        Options: map[string]string{
            "com.docker.network.bridge.enable_ip_masquerade": "false",
        },
    })
    if err != nil {
        return fmt.Errorf("failed to create network: %w", err)
    }
    d.networkID = netResp.ID
    
    // 3. 创建临时卷
    volResp, err := d.client.VolumeCreate(ctx, volume.VolumeCreateBody{
        Driver: "local",
        Name:   "codeexec-tmp",
        DriverOpts: map[string]string{
            "type":   "tmpfs",
            "device": "tmpfs",
            "o":      "size=100m,uid=1000",
        },
    })
    if err != nil {
        return fmt.Errorf("failed to create volume: %w", err)
    }
    d.volumeID = volResp.Name
    
    return nil
}

// Execute 执行代码
func (d *DockerExecutor) Execute(ctx context.Context, code string, limits ResourceLimits) (*ExecutionResult, error) {
    // 1. 容器配置
    containerConfig := &container.Config{
        Image:           d.image,
        Cmd:            []string{"python3", "-c", code},
        AttachStdout:   true,
        AttachStderr:   true,
        AttachStdin:    false,
        Tty:            false,
        NetworkDisabled: false,  // 使用隔离网络
        WorkingDir:     "/workspace",
        User:           "1000:1000",  // 非root用户
        
        // 环境变量
        Env: []string{
            "PYTHONDONTWRITEBYTECODE=1",
            "PYTHONUNBUFFERED=1",
            "HOME=/tmp",
            "TMPDIR=/tmp",
            "PATH=/usr/local/bin:/usr/bin:/bin",
        },
        
        // 健康检查
        Healthcheck: &container.HealthConfig{
            Test:     []string{"CMD", "true"},
            Interval: 5 * time.Second,
            Timeout:  3 * time.Second,
            Retries:  1,
        },
    }
    
    // 2. 主机配置（资源限制和安全）
    hostConfig := &container.HostConfig{
        // 资源限制
        Resources: container.Resources{
            // CPU限制
            CPUQuota:  int64(limits.CPUPercent) * 1000,  // microseconds
            CPUPeriod: 100000,                            // 100ms
            CPUShares: 512,                               // 相对权重
            CpusetCpus: limits.CPUCores,                  // CPU核心绑定
            
            // 内存限制
            Memory:     int64(limits.MemoryMB) * 1024 * 1024,
            MemorySwap: int64(limits.MemoryMB) * 1024 * 1024, // 禁用swap
            MemoryReservation: int64(limits.MemoryMB) * 1024 * 1024 * 80 / 100,
            KernelMemory: 50 * 1024 * 1024,  // 内核内存50MB
            
            // 进程数限制
            PidsLimit: &limits.PidsMax,
            
            // I/O限制
            BlkioDeviceReadBps: []*blkiodev.ThrottleDevice{{
                Path: "/dev/sda",
                Rate: uint64(limits.IOReadBps),
            }},
            BlkioDeviceWriteBps: []*blkiodev.ThrottleDevice{{
                Path: "/dev/sda",
                Rate: uint64(limits.IOWriteBps),
            }},
            BlkioDeviceReadIOps: []*blkiodev.ThrottleDevice{{
                Path: "/dev/sda",
                Rate: uint64(limits.IOReadIOPS),
            }},
            BlkioDeviceWriteIOps: []*blkiodev.ThrottleDevice{{
                Path: "/dev/sda",
                Rate: uint64(limits.IOWriteIOPS),
            }},
            
            // 文件描述符限制
            Ulimits: []*units.Ulimit{
                {Name: "nofile", Soft: 100, Hard: 100},
                {Name: "nproc", Soft: 50, Hard: 50},
                {Name: "core", Soft: 0, Hard: 0},
            },
        },
        
        // 安全配置
        ReadonlyRootfs: true,  // 只读根文件系统
        
        // 临时文件系统
        Tmpfs: map[string]string{
            "/tmp":       "size=10m,noexec",
            "/workspace": "size=10m",
        },
        
        // 挂载
        Mounts: []mount.Mount{
            {
                Type:   mount.TypeVolume,
                Source: d.volumeID,
                Target: "/data",
                ReadOnly: false,
            },
        },
        
        // Capabilities（移除所有危险权限）
        CapDrop: []string{
            "ALL",
        },
        CapAdd: []string{
            // 不添加任何capability
        },
        
        // 安全选项
        SecurityOpt: []string{
            "no-new-privileges",
            "apparmor=docker-default",
            "seccomp=default.json",
        },
        
        // 其他安全配置
        Privileged:     false,
        PublishAllPorts: false,
        PidMode:        "private",
        IpcMode:        "private",
        UTSMode:        "private",
        UsernsMode:     "private",
        CgroupnsMode:   "private",
        
        // 自动清理
        AutoRemove: true,
        
        // 日志配置
        LogConfig: container.LogConfig{
            Type: "json-file",
            Config: map[string]string{
                "max-size": "10m",
                "max-file": "1",
            },
        },
    }
    
    // 3. 网络配置
    networkConfig := &network.NetworkingConfig{
        EndpointsConfig: map[string]*network.EndpointSettings{
            d.networkID: {
                NetworkID: d.networkID,
            },
        },
    }
    
    // 4. 创建容器
    resp, err := d.client.ContainerCreate(
        ctx,
        containerConfig,
        hostConfig,
        networkConfig,
        nil,
        fmt.Sprintf("codeexec-%d", time.Now().UnixNano()),
    )
    if err != nil {
        return nil, fmt.Errorf("failed to create container: %w", err)
    }
    
    // 确保容器被清理
    defer func() {
        removeCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
        defer cancel()
        d.client.ContainerRemove(removeCtx, resp.ID, types.ContainerRemoveOptions{
            Force: true,
        })
    }()
    
    // 5. 启动容器
    if err := d.client.ContainerStart(ctx, resp.ID, types.ContainerStartOptions{}); err != nil {
        return nil, fmt.Errorf("failed to start container: %w", err)
    }
    
    // 6. 设置执行超时
    execCtx, cancel := context.WithTimeout(ctx, d.execTimeout)
    defer cancel()
    
    // 7. 等待容器结束
    statusCh, errCh := d.client.ContainerWait(execCtx, resp.ID, container.WaitConditionNotRunning)
    
    // 8. 获取输出
    logOptions := types.ContainerLogsOptions{
        ShowStdout: true,
        ShowStderr: true,
        Follow:     true,
        Timestamps: false,
    }
    
    logReader, err := d.client.ContainerLogs(execCtx, resp.ID, logOptions)
    if err != nil {
        return nil, fmt.Errorf("failed to get logs: %w", err)
    }
    defer logReader.Close()
    
    // 9. 读取输出
    var outputBuf bytes.Buffer
    _, err = stdcopy.StdCopy(&outputBuf, &outputBuf, logReader)
    if err != nil {
        return nil, fmt.Errorf("failed to read logs: %w", err)
    }
    
    // 10. 等待结果
    select {
    case err := <-errCh:
        if err != nil {
            return nil, fmt.Errorf("container wait error: %w", err)
        }
    case status := <-statusCh:
        // 11. 获取容器统计
        stats, _ := d.getContainerStats(ctx, resp.ID)
        
        return &ExecutionResult{
            Output:   outputBuf.String(),
            ExitCode: int(status.StatusCode),
            Success:  status.StatusCode == 0,
            Stats:    stats,
        }, nil
    case <-execCtx.Done():
        // 超时，强制停止容器
        d.client.ContainerKill(context.Background(), resp.ID, "KILL")
        return &ExecutionResult{
            Success: false,
            Error:   "Execution timeout",
        }, nil
    }
    
    return nil, fmt.Errorf("unexpected execution flow")
}

// getContainerStats 获取容器统计信息
func (d *DockerExecutor) getContainerStats(ctx context.Context, containerID string) (*ContainerStats, error) {
    statsReader, err := d.client.ContainerStats(ctx, containerID, false)
    if err != nil {
        return nil, err
    }
    defer statsReader.Body.Close()
    
    var stats types.StatsJSON
    if err := json.NewDecoder(statsReader.Body).Decode(&stats); err != nil {
        return nil, err
    }
    
    return &ContainerStats{
        CPUUsage:    calculateCPUPercent(&stats),
        MemoryUsage: stats.MemoryStats.Usage,
        MemoryLimit: stats.MemoryStats.Limit,
        PidsCount:   stats.PidsStats.Current,
        NetworkRx:   stats.Networks["eth0"].RxBytes,
        NetworkTx:   stats.Networks["eth0"].TxBytes,
    }, nil
}

// Cleanup 清理资源
func (d *DockerExecutor) Cleanup() error {
    ctx := context.Background()
    
    // 删除网络
    if d.networkID != "" {
        d.client.NetworkRemove(ctx, d.networkID)
    }
    
    // 删除卷
    if d.volumeID != "" {
        d.client.VolumeRemove(ctx, d.volumeID, true)
    }
    
    return nil
}
```

## 方案三：长期高安全性方案（1个月）

### 使用gVisor实现完全隔离

```go
// pkg/sandbox/gvisor_executor.go
package sandbox

import (
    "context"
    "encoding/json"
    "fmt"
    "io/ioutil"
    "os"
    "path/filepath"
    "time"
    
    specs "github.com/opencontainers/runtime-spec/specs-go"
    "github.com/google/uuid"
)

// GVisorExecutor gVisor执行器
type GVisorExecutor struct {
    runtime    string
    rootDir    string
    baseBundle string
}

// NewGVisorExecutor 创建gVisor执行器
func NewGVisorExecutor() (*GVisorExecutor, error) {
    // 检查runsc是否安装
    if _, err := exec.LookPath("runsc"); err != nil {
        return nil, fmt.Errorf("runsc not found: %w", err)
    }
    
    return &GVisorExecutor{
        runtime:    "runsc",
        rootDir:    "/var/run/gvisor",
        baseBundle: "/opt/gvisor/bundle",
    }, nil
}

// Execute 使用gVisor执行代码
func (g *GVisorExecutor) Execute(ctx context.Context, code string, limits ResourceLimits) (*ExecutionResult, error) {
    execID := uuid.New().String()
    
    // 1. 创建OCI bundle
    bundleDir := filepath.Join(g.rootDir, "bundles", execID)
    if err := g.createBundle(bundleDir, code, limits); err != nil {
        return nil, fmt.Errorf("failed to create bundle: %w", err)
    }
    defer os.RemoveAll(bundleDir)
    
    // 2. 创建OCI运行时规范
    spec := g.createOCISpec(code, limits)
    specPath := filepath.Join(bundleDir, "config.json")
    if err := g.writeSpec(specPath, spec); err != nil {
        return nil, fmt.Errorf("failed to write spec: %w", err)
    }
    
    // 3. 创建容器
    createCmd := exec.CommandContext(ctx, "runsc",
        "--root", g.rootDir,
        "--platform", "ptrace",  // 或 "kvm" for better performance
        "--network", "none",     // 禁用网络
        "--file-access", "exclusive",
        "--overlay",
        "create",
        "--bundle", bundleDir,
        execID,
    )
    
    if output, err := createCmd.CombinedOutput(); err != nil {
        return nil, fmt.Errorf("failed to create container: %w, output: %s", err, output)
    }
    
    // 确保容器被清理
    defer func() {
        deleteCmd := exec.Command("runsc",
            "--root", g.rootDir,
            "delete", execID,
        )
        deleteCmd.Run()
    }()
    
    // 4. 启动容器
    startCmd := exec.CommandContext(ctx, "runsc",
        "--root", g.rootDir,
        "start", execID,
    )
    
    if output, err := startCmd.CombinedOutput(); err != nil {
        return nil, fmt.Errorf("failed to start container: %w, output: %s", err, output)
    }
    
    // 5. 等待容器结束
    waitCmd := exec.CommandContext(ctx, "runsc",
        "--root", g.rootDir,
        "wait", execID,
    )
    
    output, err := waitCmd.CombinedOutput()
    
    result := &ExecutionResult{
        Output: string(output),
    }
    
    if err != nil {
        if ctx.Err() == context.DeadlineExceeded {
            // 超时，杀死容器
            killCmd := exec.Command("runsc",
                "--root", g.rootDir,
                "kill", execID, "KILL",
            )
            killCmd.Run()
            result.Error = "Execution timeout"
        } else {
            result.Error = err.Error()
        }
        result.Success = false
    } else {
        result.Success = true
    }
    
    // 6. 获取容器状态
    if state, err := g.getContainerState(execID); err == nil {
        result.ExitCode = state.ExitCode
        result.Stats = state.Stats
    }
    
    return result, nil
}

// createOCISpec 创建OCI运行时规范
func (g *GVisorExecutor) createOCISpec(code string, limits ResourceLimits) *specs.Spec {
    return &specs.Spec{
        Version: specs.Version,
        
        // 进程配置
        Process: &specs.Process{
            Terminal: false,
            User: specs.User{
                UID: 1000,
                GID: 1000,
            },
            Args: []string{
                "python3", "-c", code,
            },
            Env: []string{
                "PATH=/usr/local/bin:/usr/bin:/bin",
                "PYTHONDONTWRITEBYTECODE=1",
                "PYTHONUNBUFFERED=1",
                "HOME=/tmp",
                "TMPDIR=/tmp",
            },
            Cwd: "/workspace",
            
            // Capabilities - 移除所有
            Capabilities: &specs.LinuxCapabilities{
                Bounding:    []string{},
                Effective:   []string{},
                Inheritable: []string{},
                Permitted:   []string{},
                Ambient:     []string{},
            },
            
            // 资源限制
            Rlimits: []specs.POSIXRlimit{
                {
                    Type: "RLIMIT_AS",
                    Hard: uint64(limits.MemoryMB * 1024 * 1024),
                    Soft: uint64(limits.MemoryMB * 1024 * 1024),
                },
                {
                    Type: "RLIMIT_NPROC",
                    Hard: uint64(limits.PidsMax),
                    Soft: uint64(limits.PidsMax),
                },
                {
                    Type: "RLIMIT_NOFILE",
                    Hard: uint64(limits.FDLimit),
                    Soft: uint64(limits.FDLimit),
                },
                {
                    Type: "RLIMIT_FSIZE",
                    Hard: uint64(limits.FileSizeMB * 1024 * 1024),
                    Soft: uint64(limits.FileSizeMB * 1024 * 1024),
                },
                {
                    Type: "RLIMIT_CPU",
                    Hard: uint64(30),  // 30秒CPU时间
                    Soft: uint64(30),
                },
            },
            
            NoNewPrivileges: true,
        },
        
        // 根文件系统
        Root: &specs.Root{
            Path:     "rootfs",
            Readonly: true,
        },
        
        // 挂载点
        Mounts: []specs.Mount{
            {
                Destination: "/proc",
                Type:        "proc",
                Source:      "proc",
                Options:     []string{"nosuid", "noexec", "nodev"},
            },
            {
                Destination: "/dev",
                Type:        "tmpfs",
                Source:      "tmpfs",
                Options:     []string{"nosuid", "strictatime", "mode=755", "size=65536k"},
            },
            {
                Destination: "/tmp",
                Type:        "tmpfs",
                Source:      "tmpfs",
                Options:     []string{"nosuid", "nodev", "noexec", "size=10m"},
            },
            {
                Destination: "/workspace",
                Type:        "tmpfs",
                Source:      "tmpfs",
                Options:     []string{"nosuid", "nodev", "size=10m"},
            },
        },
        
        // Linux特定配置
        Linux: &specs.Linux{
            // 资源管理（cgroups）
            Resources: &specs.LinuxResources{
                // 内存限制
                Memory: &specs.LinuxMemory{
                    Limit:       &[]int64{int64(limits.MemoryMB * 1024 * 1024)}[0],
                    Reservation: &[]int64{int64(limits.MemoryMB * 1024 * 1024 * 80 / 100)}[0],
                    Swap:        &[]int64{0}[0],  // 禁用swap
                },
                
                // CPU限制
                CPU: &specs.LinuxCPU{
                    Shares: &[]uint64{512}[0],
                    Quota:  &[]int64{int64(limits.CPUQuotaPercent * 1000)}[0],
                    Period: &[]uint64{100000}[0],
                },
                
                // 进程数限制
                Pids: &specs.LinuxPids{
                    Limit: int64(limits.PidsMax),
                },
                
                // 块设备I/O限制
                BlockIO: &specs.LinuxBlockIO{
                    Weight: &[]uint16{100}[0],
                    ThrottleReadBpsDevice: []specs.LinuxThrottleDevice{
                        {
                            Major: 8,
                            Minor: 0,
                            Rate:  uint64(limits.IOReadBps),
                        },
                    },
                    ThrottleWriteBpsDevice: []specs.LinuxThrottleDevice{
                        {
                            Major: 8,
                            Minor: 0,
                            Rate:  uint64(limits.IOWriteBps),
                        },
                    },
                },
            },
            
            // Namespace配置
            Namespaces: []specs.LinuxNamespace{
                {Type: "pid"},
                {Type: "network"},
                {Type: "ipc"},
                {Type: "uts"},
                {Type: "mount"},
                {Type: "user"},
            },
            
            // Seccomp配置（系统调用过滤）
            Seccomp: &specs.LinuxSeccomp{
                DefaultAction: "SCMP_ACT_ERRNO",
                Architectures: []specs.Arch{
                    "SCMP_ARCH_X86_64",
                },
                Syscalls: g.getAllowedSyscalls(),
            },
            
            // 掩码路径（隐藏敏感路径）
            MaskedPaths: []string{
                "/proc/kcore",
                "/proc/latency_stats",
                "/proc/timer_list",
                "/proc/timer_stats",
                "/proc/sched_debug",
                "/sys/firmware",
            },
            
            // 只读路径
            ReadonlyPaths: []string{
                "/proc/asound",
                "/proc/bus",
                "/proc/fs",
                "/proc/irq",
                "/proc/sys",
                "/proc/sysrq-trigger",
            },
        },
    }
}

// getAllowedSyscalls 返回允许的系统调用列表
func (g *GVisorExecutor) getAllowedSyscalls() []specs.LinuxSyscall {
    // 仅允许Python执行必需的系统调用
    allowedCalls := []string{
        "read", "write", "close", "fstat", "lseek",
        "mmap", "mprotect", "munmap", "brk", "rt_sigaction",
        "rt_sigprocmask", "ioctl", "access", "execve", "getdents",
        "getcwd", "openat", "stat", "exit", "exit_group",
        "getpid", "getppid", "arch_prctl", "gettid", "futex",
        "set_tid_address", "clock_gettime", "clock_getres",
    }
    
    syscalls := make([]specs.LinuxSyscall, 0, len(allowedCalls))
    for _, name := range allowedCalls {
        syscalls = append(syscalls, specs.LinuxSyscall{
            Names:  []string{name},
            Action: "SCMP_ACT_ALLOW",
        })
    }
    
    return syscalls
}

// createBundle 创建OCI bundle
func (g *GVisorExecutor) createBundle(bundleDir string, code string, limits ResourceLimits) error {
    // 创建bundle目录结构
    rootfsDir := filepath.Join(bundleDir, "rootfs")
    if err := os.MkdirAll(rootfsDir, 0755); err != nil {
        return err
    }
    
    // 复制基础rootfs
    if err := copyDir(g.baseBundle, rootfsDir); err != nil {
        return err
    }
    
    // 创建工作目录
    workDir := filepath.Join(rootfsDir, "workspace")
    if err := os.MkdirAll(workDir, 0755); err != nil {
        return err
    }
    
    // 写入Python脚本
    scriptPath := filepath.Join(workDir, "script.py")
    if err := ioutil.WriteFile(scriptPath, []byte(code), 0644); err != nil {
        return err
    }
    
    return nil
}

// writeSpec 写入OCI规范
func (g *GVisorExecutor) writeSpec(path string, spec *specs.Spec) error {
    data, err := json.MarshalIndent(spec, "", "  ")
    if err != nil {
        return err
    }
    return ioutil.WriteFile(path, data, 0644)
}
```

## 📊 方案对比与选择

### 性能与安全性对比

| 指标 | ulimit | cgroups v2 | Docker | gVisor | Firecracker |
|------|--------|------------|--------|---------|------------|
| **启动时间** | <10ms | <50ms | 200-500ms | 1-2s | 100-200ms |
| **内存开销** | ~0MB | ~1MB | ~50MB | ~20MB | ~5MB |
| **CPU开销** | <1% | <2% | 5-10% | 10-20% | 2-5% |
| **安全级别** | ⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **实现复杂度** | ⭐ | ⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **维护成本** | 低 | 中 | 低 | 高 | 高 |

### 资源限制能力对比

| 资源类型 | ulimit | cgroups v2 | Docker | gVisor |
|---------|--------|------------|--------|---------|
| CPU配额 | ❌ | ✅ | ✅ | ✅ |
| 内存限制 | ⚠️ | ✅ | ✅ | ✅ |
| I/O带宽 | ❌ | ✅ | ✅ | ✅ |
| 网络隔离 | ❌ | ⚠️ | ✅ | ✅ |
| 系统调用过滤 | ❌ | ⚠️ | ✅ | ✅ |
| 文件系统隔离 | ❌ | ⚠️ | ✅ | ✅ |

## 🎯 实施建议

### 阶段性实施路线

#### 第一阶段：紧急修复（1-3天）
1. **立即行动**：
   - 修复Dockerfile中的Go版本错误
   - 增加完整的ulimit限制
   - 禁用网络访问（unshare -n）
   - 添加基本的代码验证

2. **快速改进**：
   ```bash
   # 改进的执行命令
   unshare -n -p -m -i -u bash -c '
     ulimit -v 262144;    # 虚拟内存256MB
     ulimit -t 30;        # CPU时间30秒
     ulimit -u 50;        # 最多50个进程
     ulimit -n 100;       # 100个文件描述符
     ulimit -f 10240;     # 文件大小10MB
     exec python3 -c "$CODE"
   '
   ```

#### 第二阶段：cgroups集成（1周）
1. 实现cgroups v2管理器
2. 添加资源监控
3. 实现优雅降级（cgroups不可用时回退到ulimit）
4. 添加Prometheus指标

#### 第三阶段：容器化（2-3周）
1. 评估Docker vs containerd
2. 实现容器池管理
3. 优化镜像大小和启动时间
4. 添加容器健康检查

#### 第四阶段：高安全沙箱（1个月）
1. 评估gVisor vs Firecracker
2. 实现完整隔离方案
3. 性能调优
4. 生产环境测试

### 监控指标实现

```go
// pkg/metrics/resource_metrics.go
package metrics

import (
    "github.com/prometheus/client_golang/prometheus"
    "github.com/prometheus/client_golang/prometheus/promauto"
)

var (
    // CPU使用率
    cpuUsage = promauto.NewGaugeVec(prometheus.GaugeOpts{
        Name: "codeexec_cpu_usage_percent",
        Help: "CPU usage percentage by execution",
    }, []string{"exec_id", "user_id"})
    
    // 内存使用
    memoryUsage = promauto.NewGaugeVec(prometheus.GaugeOpts{
        Name: "codeexec_memory_usage_bytes",
        Help: "Memory usage in bytes",
    }, []string{"exec_id", "user_id"})
    
    // I/O操作
    ioOperations = promauto.NewCounterVec(prometheus.CounterOpts{
        Name: "codeexec_io_operations_total",
        Help: "Total I/O operations",
    }, []string{"exec_id", "operation"})
    
    // 资源限制违规
    limitViolations = promauto.NewCounterVec(prometheus.CounterOpts{
        Name: "codeexec_limit_violations_total",
        Help: "Resource limit violations",
    }, []string{"resource_type", "user_id"})
    
    // 执行时间分布
    executionDuration = promauto.NewHistogramVec(prometheus.HistogramOpts{
        Name:    "codeexec_execution_duration_seconds",
        Help:    "Execution duration distribution",
        Buckets: []float64{0.1, 0.5, 1, 2, 5, 10, 30},
    }, []string{"status"})
)
```

## 总结

资源限制的完整性对系统安全性至关重要。当前实现存在严重安全隐患，建议：

1. **立即修复**：增加基本的资源限制，修复已知问题
2. **短期改进**：实现cgroups v2支持，提供细粒度控制
3. **中期目标**：容器化执行环境，利用成熟技术栈
4. **长期规划**：实现高安全性沙箱，达到生产级别

通过分阶段实施，可以在保证系统可用性的同时，逐步提升安全性和资源管理能力。