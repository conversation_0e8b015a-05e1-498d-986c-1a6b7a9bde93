# Python Package Usage Best Practices

## 🚀 Quick Start

The Code Executor Service supports a wide range of Python packages but some packages require special consideration due to their initialization overhead.

## ⚡ Performance Categories

### Fast Import Packages (< 1s)
```python
# Standard Library - Always fast
import os, sys, json, time, math, random

# Lightweight utilities
import requests      # HTTP client
import yaml          # Configuration files  
import base64        # Encoding/decoding
import datetime      # Date handling
```

### Medium Import Packages (1-10s)
```python
# Data utilities
import ujson         # Fast JSON parsing
from PIL import Image # Image processing basics
import dateutil      # Advanced date parsing

# Web scraping
import bs4           # HTML parsing
from lxml import html # XML processing
```

### Heavy Import Packages (10s+ first import)
```python
# Data analysis - First import may take 30-120s
import pandas as pd
import numpy as np
import scipy

# Visualization - First import may take 15-60s  
import matplotlib.pyplot as plt
import seaborn as sns

# Machine Learning - Very heavy (60s+)
import sklearn
import tensorflow as tf
import torch
```

## 🎯 Optimization Strategies

### 1. Selective Imports
**❌ Avoid**: Full package imports
```python
import pandas  # Imports entire pandas library
import numpy   # Imports all numpy modules
```

**✅ Prefer**: Selective imports
```python
from pandas import DataFrame, read_csv    # Import only needed classes
from numpy import array, mean, std       # Import only needed functions
from matplotlib.pyplot import plot, show # Import only needed functions
```

### 2. Import Ordering
**✅ Best Practice**: Import heavy packages first
```python
# Heavy packages first
import pandas as pd
import numpy as np

# Your main logic here
data = pd.DataFrame({'x': [1, 2, 3], 'y': [4, 5, 6]})
result = np.mean(data['x'])
```

### 3. Alternative Lightweight Options

| Heavy Package | Lightweight Alternative | Use Case |
|--------------|-------------------------|----------|
| `pandas` | `json` + `list` comprehensions | Simple data manipulation |
| `numpy` | Built-in `math` module | Basic mathematical operations |
| `matplotlib` | Print statements with ASCII | Simple data display |
| `requests` + `BeautifulSoup` | `urllib` + string methods | Simple web scraping |
| `scipy` | Built-in `statistics` | Basic statistical functions |

### 4. Code Structure Optimization
**✅ Efficient pattern**:
```python
# Import once at the top
import pandas as pd

# Process data efficiently  
def analyze_data(data_list):
    df = pd.DataFrame(data_list)
    return df.describe()

# Main logic
data = [{'a': 1, 'b': 2}, {'a': 3, 'b': 4}]
result = analyze_data(data)
print(result)
```

## 🔧 Environment Configuration

### Current Service Limits
- **Timeout**: 300 seconds (5 minutes) execution limit
- **Memory**: 256MB default limit  
- **First Import**: Heavy packages may take 30-120s on first use
- **Package Cache**: Bytecode pre-compilation enabled for faster subsequent imports

### Pre-installed Packages
The service includes optimized versions of common packages:
- **Data**: numpy (>=2.3.2), pandas (>=2.3.2), scipy (>=1.16.1)
- **Web**: requests (>=2.32.5), httpx (>=0.28.1)
- **Utilities**: pyyaml, pillow, python-dateutil, ujson
- **Visualization**: matplotlib (>=3.10.6), seaborn (>=0.13.2)
- **Parsing**: beautifulsoup4, lxml, openpyxl
- **Templates**: jinja2

## 📊 Performance Tips

### Understanding Execution Times
- **0-5s**: Optimal performance range
- **5-30s**: Normal for basic data operations
- **30-120s**: Expected for heavy package first import
- **120s+**: Consider optimization (check API warnings)

### API Response Warnings
The service provides automatic performance warnings:
- **Heavy Package Detection**: Identifies packages that may cause delays
- **Execution Time Alerts**: Warns when execution exceeds thresholds  
- **Optimization Suggestions**: Provides specific improvement recommendations

Example warning response:
```json
{
  "id": "uuid-here",
  "success": true,
  "exec_time_ms": 45000,
  "output": "Analysis complete", 
  "warning": "💡 Heavy packages detected: pandas, numpy. First import may take longer (45s). Subsequent runs will be faster within the same session."
}
```

## 🚨 Common Issues & Solutions

### Issue: Import Timeout
**Symptoms**: Code fails with timeout error during package import

**Solutions**:
1. **Use selective imports**: `from pandas import DataFrame` vs `import pandas`
2. **Optimize algorithm**: Reduce computational complexity
3. **Split operations**: Break large operations into smaller chunks
4. **Consider alternatives**: Use lighter packages for simple operations

### Issue: Memory Limit Exceeded  
**Symptoms**: Process killed due to memory usage

**Solutions**:
1. **Process data in chunks**: Use pandas chunking for large datasets
2. **Clear variables**: Use `del variable` to free memory
3. **Use generators**: Process data lazily instead of loading everything
4. **Optimize data types**: Use appropriate dtypes in pandas/numpy

### Issue: Long Response Times
**Symptoms**: Slow execution even for simple operations

**Solutions**:
1. **Profile your code**: Identify bottlenecks
2. **Cache results**: Store intermediate results
3. **Vectorize operations**: Use numpy/pandas vectorized functions
4. **Avoid loops**: Use built-in functions instead of Python loops

## 🔗 Example Usage Patterns

### Efficient Data Analysis
```python
# Optimized data processing example
from pandas import DataFrame
from numpy import mean, std

# Simple data creation and analysis
data = {'sales': [100, 150, 200, 175], 'month': ['Jan', 'Feb', 'Mar', 'Apr']}
df = DataFrame(data)

# Efficient calculations
avg_sales = mean(df['sales'])
std_sales = std(df['sales']) 

print(f"Average: {avg_sales:.1f}, Std Dev: {std_sales:.1f}")
```

### Web Data Processing
```python
# Lightweight web scraping
import requests
from bs4 import BeautifulSoup

response = requests.get('https://httpbin.org/json')
data = response.json()
print(f"Origin IP: {data['origin']}")
```

### Visualization Alternative
```python
# ASCII-based data display instead of matplotlib
data = [10, 25, 17, 30, 22]
max_val = max(data)

print("Simple Bar Chart:")
for i, val in enumerate(data):
    bar = '█' * int((val / max_val) * 20)
    print(f"Item {i+1}: {bar} ({val})")
```

## 💡 Advanced Tips

1. **Batch Processing**: Group related operations to maximize package reuse
2. **Caching Strategy**: Store results of expensive operations
3. **Profile First**: Measure before optimizing
4. **Monitor Warnings**: Use API warning responses to guide optimization
5. **Test Locally**: Develop locally with similar package versions for faster iteration

---

For more information about the Code Executor Service, see `CLAUDE.md` in the project root.