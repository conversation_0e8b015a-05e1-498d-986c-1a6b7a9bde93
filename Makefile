# Makefile for Code Executor Service

# Variables
APP_NAME := code-sandbox
DOCKER_IMAGE := $(APP_NAME):latest
DOCKER_TAG := $(APP_NAME):$(shell git rev-parse --short HEAD 2>/dev/null || echo "latest")
BUILD_DIR := build
CMD_DIR := cmd
MAIN_FILE := $(CMD_DIR)/main.go

# Go build flags
LDFLAGS := -ldflags="-s -w"
GO_FLAGS := CGO_ENABLED=0

.PHONY: help build clean test test-coverage test-bench run docker-build docker-run docker-stop dev install fmt lint vet tidy

# Default target
help: ## Show this help message
	@echo 'Usage: make <target>'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Build targets
build: ## Build the application binary
	@echo "Building $(APP_NAME)..."
	@mkdir -p $(BUILD_DIR)
	@$(GO_FLAGS) go build $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME) ./$(CMD_DIR)
	@echo "Built: $(BUILD_DIR)/$(APP_NAME)"

install: ## Install dependencies
	@echo "Installing dependencies..."
	@go mod download
	@go mod tidy

# Development targets
dev: ## Run the application in development mode
	@echo "Starting development server..."
	@go run ./$(CMD_DIR)

run: build ## Build and run the application
	@echo "Running $(APP_NAME)..."
	@./$(BUILD_DIR)/$(APP_NAME)

# Testing targets
test: ## Run all tests
	@echo "Running tests..."
	@TEST_AUTH_TOKEN="your-secret-token-here" go test -v ./...

test-coverage: ## Run tests with coverage
	@echo "Running tests with coverage..."
	@go test -coverprofile=coverage.out ./...
	@go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report: coverage.html"

test-bench: ## Run benchmark tests
	@echo "Running benchmark tests..."
	@go test -bench=. -benchmem ./...

# Docker targets
docker-build: ## Build Docker image
	@echo "Building Docker image..."
	@docker build -f docker/Dockerfile -t $(DOCKER_IMAGE) .
	@docker tag $(DOCKER_IMAGE) $(DOCKER_TAG)
	@echo "Built: $(DOCKER_IMAGE) and $(DOCKER_TAG)"

docker-run: ## Run Docker container
	@echo "Running Docker container..."
	@docker run -d \
		--name $(APP_NAME) \
		-p 8080:8080 \
		-e AUTH_TOKEN=secret-token-123456 \
		$(DOCKER_IMAGE)
	@echo "Container started: http://localhost:8080"

docker-stop: ## Stop and remove Docker container
	@echo "Stopping Docker container..."
	@docker stop $(APP_NAME) 2>/dev/null || true
	@docker rm $(APP_NAME) 2>/dev/null || true

docker-compose-up: ## Start services with docker-compose
	@echo "Starting services with docker-compose..."
	@docker-compose -f docker/docker-compose.yml up --build -d
	@echo "Services started: http://localhost:8080"

docker-compose-down: ## Stop services with docker-compose
	@echo "Stopping services with docker-compose..."
	@docker-compose -f docker/docker-compose.yml down

# Code quality targets
fmt: ## Format Go code
	@echo "Formatting code..."
	@go fmt ./...

vet: ## Run go vet
	@echo "Running go vet..."
	@go vet ./...

lint: ## Run golangci-lint
	@echo "Running golangci-lint..."
	@golangci-lint run ./...

tidy: ## Tidy up go.mod
	@echo "Tidying go.mod..."
	@go mod tidy

# Cleanup targets
clean: ## Clean build artifacts
	@echo "Cleaning up..."
	@rm -rf $(BUILD_DIR)
	@rm -f coverage.out coverage.html
	@docker rmi $(DOCKER_IMAGE) 2>/dev/null || true
	@docker rmi $(DOCKER_TAG) 2>/dev/null || true

clean-docker: ## Remove all Docker containers and images
	@echo "Cleaning Docker..."
	@docker stop $(APP_NAME) 2>/dev/null || true
	@docker rm $(APP_NAME) 2>/dev/null || true
	@docker rmi $(DOCKER_IMAGE) 2>/dev/null || true
	@docker rmi $(DOCKER_TAG) 2>/dev/null || true
	@cd docker && docker-compose down --rmi all --volumes --remove-orphans 2>/dev/null || true


# All-in-one targets
all: clean install fmt vet build test ## Run all checks and build

deploy: clean install test docker-build ## Prepare for deployment

quick: build run ## Quick build and run