#!/usr/bin/env python3
"""
Python包预热脚本
在容器启动时预导入常用包，优化首次导入性能
"""

import sys
import time
import logging
import importlib

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 需要预热的包列表
WARMUP_PACKAGES = [
    # Python标准库
    'json',
    'base64', 
    'time',
    'datetime',
    'os',
    'sys',
    'math',
    'random',
    'collections',
    'functools',
    'itertools',
    
    # 数据处理
    'numpy',
    'pandas',
    'glom',
    'scipy',
    
    # HTTP请求库
    'requests',
    'httpx',
    
    # 数据可视化
    'matplotlib',
    'matplotlib.pyplot',
    'seaborn',
    
    # 实用工具
    'yaml',
    'PIL',
    'dateutil',
    
    # HTML/XML解析
    'bs4',
    'lxml',
    'openpyxl',
    
    # JSON处理优化
    'ujson',
    
    # 其它
    'jinja2'
]

def warmup_package(package_name: str) -> tuple[bool, float]:
    """
    预热单个包
    
    Args:
        package_name: 包名
        
    Returns:
        (success, import_time)
    """
    start_time = time.time()
    try:
        # 尝试导入包
        importlib.import_module(package_name)
        import_time = time.time() - start_time
        logger.info(f"✅ {package_name:20s} - {import_time:.3f}s")
        return True, import_time
    except ImportError as e:
        import_time = time.time() - start_time
        logger.warning(f"⚠️  {package_name:20s} - 导入失败: {e}")
        return False, import_time
    except Exception as e:
        import_time = time.time() - start_time
        logger.error(f"❌ {package_name:20s} - 异常: {e}")
        return False, import_time

def main():
    """主函数：执行包预热"""
    logger.info("🚀 开始Python包预热...")
    start_time = time.time()
    
    success_count = 0
    total_packages = len(WARMUP_PACKAGES)
    
    for package in WARMUP_PACKAGES:
        success, _ = warmup_package(package)
        if success:
            success_count += 1
    
    total_time = time.time() - start_time
    
    logger.info(f"🎯 预热完成！")
    logger.info(f"📊 成功: {success_count}/{total_packages}")
    logger.info(f"⏱️  总耗时: {total_time:.2f}s")
    logger.info(f"🔥 Python包预热就绪，导入性能已优化")

if __name__ == "__main__":
    main()