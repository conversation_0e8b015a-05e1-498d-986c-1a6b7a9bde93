#!/usr/bin/env python3

import ast
import base64
import binascii
import json
import os
import re
import signal
import sys
import time
from dataclasses import asdict, dataclass
from enum import IntEnum
from typing import Any, Optional


class SecurityRisk(IntEnum):
    SAFE = 0
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class SecurityViolation:
    type: str
    description: str
    risk: int
    line: Optional[int] = None
    column: Optional[int] = None
    pattern: Optional[str] = None
    context: Optional[str] = None


@dataclass
class ValidationResult:
    safe: bool
    risk: int
    violations: list[dict[str, Any]]
    duration_ms: float
    analysis_type: str = "ast_based"


class SecurityValidator(ast.NodeVisitor):
    """Optimized AST-based security validator for Python code sandboxes."""
    
    def __init__(self, config: dict[str, Any]):
        self.violations: List[SecurityViolation] = []
        self.config = config
        
        # Blocked imports configuration
        self.blocked_imports: Set[str] = set(config.get('blocked_imports', []))
        
        # Network requests configuration
        network_config = config.get('network_requests', {})
        self.allow_network_requests = network_config.get('allow', True)
        self.network_blocked_modules: Set[str] = set(network_config.get('blocked_when_disabled', []))
        self.network_risk_level = self._parse_risk_level(network_config.get('risk_level', 'critical'))
        
        # Resource limits configuration (replaces hardcoded values)
        limits = config.get('resource_limits', {})
        self.max_loop_iterations = limits.get('max_loop_iterations', 1000000)
        self.max_memory_allocation = limits.get('max_memory_allocation', 100000000)
        self.max_string_repetition = limits.get('max_string_repetition', 10000000)
        self.max_exponentiation = limits.get('max_exponentiation', 1000)
        self.max_loop_nesting = limits.get('max_loop_nesting', 3)
        self.max_comprehension_nesting = limits.get('max_comprehension_nesting', 2)
        
        # Validator behavior configuration
        validator_config = config.get('validator', {})
        self.early_termination = validator_config.get('early_termination', True)
        self.max_violations = validator_config.get('max_violations', 50)

        # Performance optimization attributes
        self.critical_found = False
        
        # Track analysis state for complex pattern detection
        self.current_line = 1
        self.function_names: Set[str] = set()
        self.loop_nesting = 0
        self.class_nesting = 0
        self.memory_operations = 0  # Track potential memory bombs
        
        # Pre-compile dangerous patterns for performance
        self._dangerous_call_patterns = {
            'eval', 'exec', 'compile', '__import__', 'getattr', 'setattr', 'delattr',
            'globals', 'locals', 'vars', 'dir', 'memoryview', 'open'
        }
        
        # Dangerous attribute patterns (pre-compiled for performance)
        self._dangerous_attrs = re.compile(r'^__[a-zA-Z_][a-zA-Z0-9_]*__$')
        
        # Network request functions to monitor
        self._network_functions = {
            'urlopen', 'urlretrieve', 'request', 'get', 'post', 
            'put', 'delete', 'patch', 'head', 'options', 'connect'
        }
    
    def _parse_risk_level(self, risk_level_str: str) -> SecurityRisk:
        """Parse risk level string to SecurityRisk enum."""
        risk_mapping = {
            'safe': SecurityRisk.SAFE,
            'low': SecurityRisk.LOW,
            'medium': SecurityRisk.MEDIUM,
            'high': SecurityRisk.HIGH,
            'critical': SecurityRisk.CRITICAL
        }
        return risk_mapping.get(risk_level_str.lower(), SecurityRisk.CRITICAL)
        
    def add_violation(self, violation_type: str, description: str, risk: SecurityRisk, 
                     node: Optional[ast.AST] = None, pattern: Optional[str] = None):
        """Add a security violation with performance optimizations."""
        # Prevent memory exhaustion from too many violations
        if len(self.violations) >= self.max_violations:
            return
            
        line = getattr(node, 'lineno', self.current_line) if node else None
        column = getattr(node, 'col_offset', None) if node else None
        
        violation = SecurityViolation(
            type=violation_type,
            description=description,
            risk=risk.value,
            line=line,
            column=column,
            pattern=pattern
        )
        self.violations.append(violation)
        
        # Set flag for early termination on critical risks
        if risk == SecurityRisk.CRITICAL:
            self.critical_found = True

    def visit_Import(self, node: ast.Import) -> None:
        """Check import statements for blocked modules."""
        if not self._should_continue_analysis():
            return
            
        for alias in node.names:
            self._check_import_security(alias.name, node)
            if not self._should_continue_analysis():
                return
        self.generic_visit(node)

    def visit_ImportFrom(self, node: ast.ImportFrom) -> None:
        """Check from...import statements."""
        if not self._should_continue_analysis():
            return
            
        if node.module:
            self._check_import_security(node.module, node)
            
            # Check for dangerous wildcard imports
            for alias in node.names:
                if alias.name == '*':
                    self.add_violation(
                        'wildcard_import',
                        f'Wildcard import from {node.module} (security risk)',
                        SecurityRisk.HIGH,  # Elevated severity for sandbox
                        node,
                        f'from {node.module} import *'
                    )
        self.generic_visit(node)

    def _check_import_security(self, module_name: str, node: ast.AST) -> None:
        """Enhanced import security check with network request control."""
        # Check blocked imports - single source of truth
        for blocked in self.blocked_imports:
            if module_name == blocked or module_name.startswith(blocked + '.'):
                self.add_violation(
                    'blocked_import',
                    f'Blocked module import: {module_name}',
                    SecurityRisk.CRITICAL,
                    node,
                    module_name
                )
                return  # Early return on critical violation
        
        # Check network request imports if disabled
        if not self.allow_network_requests:
            self._check_network_import(module_name, node)

    def _check_network_import(self, module_name: str, node: ast.AST) -> None:
        """Check network-related module imports when network requests are disabled."""
        for network_module in self.network_blocked_modules:
            if module_name == network_module or module_name.startswith(network_module + '.'):
                self.add_violation(
                    'network_request_blocked',
                    f'Network request module blocked: {module_name} (network requests disabled)',
                    self.network_risk_level,
                    node,
                    module_name
                )
                return

    def visit_Call(self, node: ast.Call) -> None:
        """Enhanced call detection for sandbox security."""
        if not self._should_continue_analysis():
            return
            
        func_name = self._get_function_name(node.func)
        
        if func_name:
            # Critical security violations (immediate blocking)
            if func_name in ['eval', 'exec', 'compile']:
                self.add_violation(
                    'code_injection',
                    f'Dynamic code execution detected: {func_name}()',
                    SecurityRisk.CRITICAL,
                    node,
                    func_name
                )
            elif func_name == '__import__':
                self.add_violation(
                    'dynamic_import',
                    f'Dynamic import detected: {func_name}()',
                    SecurityRisk.CRITICAL,
                    node,
                    func_name
                )
            elif func_name in ['getattr', 'setattr', 'delattr']:
                self._check_dangerous_attribute_access(node, func_name)
            elif func_name in ['globals', 'locals', 'vars']:
                self.add_violation(
                    'namespace_manipulation',
                    f'Namespace manipulation detected: {func_name}()',
                    SecurityRisk.HIGH,
                    node,
                    func_name
                )
            elif func_name == 'open':
                self.add_violation(
                    'file_access',
                    'File system access detected',
                    SecurityRisk.HIGH,
                    node,
                    func_name
                )
            elif func_name == 'memoryview':
                self.add_violation(
                    'memory_access',
                    'Direct memory access detected: memoryview()',
                    SecurityRisk.HIGH,
                    node,
                    func_name
                )
            
            # Check for resource exhaustion patterns
            self._check_resource_exhaustion_call(node, func_name)
            
            # Check for sandbox escape attempts
            self._check_sandbox_escape_call(node, func_name)
            
            # Check for network request function calls
            if not self.allow_network_requests:
                self._check_network_call(node, func_name)
        
        self.generic_visit(node)
        
    def _check_dangerous_attribute_access(self, node: ast.Call, func_name: str) -> None:
        """Check for dangerous attribute access patterns."""
        if len(node.args) >= 2 and isinstance(node.args[1], ast.Constant):
            attr_name = node.args[1].value
            if isinstance(attr_name, str) and self._dangerous_attrs.match(attr_name):
                self.add_violation(
                    'dangerous_attribute_access',
                    f'Access to dangerous attribute: {attr_name}',
                    SecurityRisk.HIGH,
                    node,
                    f'{func_name}({attr_name})'
                )
                
    def _check_resource_exhaustion_call(self, node: ast.Call, func_name: str) -> None:
        """Check for resource exhaustion attack patterns in function calls."""
        # Memory exhaustion via large data structures
        if func_name in ['list', 'tuple', 'set', 'dict', 'bytearray']:
            if node.args:
                arg = node.args[0]
                if isinstance(arg, ast.Call) and self._get_function_name(arg.func) == 'range':
                    self._check_large_range(arg, 'memory_exhaustion_list')
                    
        # String/bytes operations that can consume excessive memory
        elif func_name == 'range':
            self._check_large_range(node, 'large_range')
            
    def _check_large_range(self, node: ast.Call, violation_type: str) -> None:
        """Check for excessively large range operations."""
        if node.args and isinstance(node.args[0], ast.Constant):
            if isinstance(node.args[0].value, int) and node.args[0].value > self.max_loop_iterations:
                self.add_violation(
                    violation_type,
                    f'Large range detected: range({node.args[0].value})',
                    SecurityRisk.MEDIUM,
                    node
                )
                
    def _check_sandbox_escape_call(self, node: ast.Call, func_name: str) -> None:
        """Check for sandbox escape attempt patterns."""
        # Type manipulation for class creation/modification
        if func_name == 'type' and len(node.args) == 3:
            self.add_violation(
                'dynamic_class_creation',
                'Dynamic class creation detected',
                SecurityRisk.HIGH,
                node,
                'type(name, bases, dict)'
            )
        # Frame inspection
        elif func_name in ['_getframe', 'currentframe']:
            self.add_violation(
                'frame_inspection',
                f'Frame inspection detected: {func_name}()',
                SecurityRisk.CRITICAL,
                node,
                func_name
            )

    def _check_network_call(self, node: ast.Call, func_name: str) -> None:
        """Check for network request function calls when network requests are disabled."""
        if func_name in self._network_functions:
            # Additional context check to avoid false positives
            context_info = self._get_call_context(node)
            if self._is_likely_network_call(node, func_name, context_info):
                self.add_violation(
                    'network_request_call',
                    f'Network request function blocked: {func_name}() (network requests disabled)',
                    self.network_risk_level,
                    node,
                    func_name
                )

    def _get_call_context(self, node: ast.Call) -> str:
        """Get context information about the function call."""
        if isinstance(node.func, ast.Attribute):
            return f"{self._get_object_name(node.func.value)}.{node.func.attr}"
        elif isinstance(node.func, ast.Name):
            return node.func.id
        return "unknown"

    def _get_object_name(self, node: ast.AST) -> str:
        """Get the name of the object being accessed."""
        if isinstance(node, ast.Name):
            return node.id
        elif isinstance(node, ast.Attribute):
            return f"{self._get_object_name(node.value)}.{node.attr}"
        return "unknown"

    def _is_likely_network_call(self, node: ast.Call, func_name: str, context: str) -> bool:
        """Determine if this is likely a network request call."""
        # Common network request patterns
        network_contexts = [
            'requests.', 'urllib.', 'httpx.', 'aiohttp.', 'http.client.',
            'socket.', 'ssl.', 'websocket.', 'ftplib.', 'smtplib.'
        ]
        
        # Check if the function is called in a network context
        for net_context in network_contexts:
            if context.startswith(net_context):
                return True
                
        # Specific function patterns that are almost always network-related
        if func_name in ['urlopen', 'urlretrieve', 'connect'] and 'unknown' not in context:
            return True
            
        return False

    def visit_Attribute(self, node: ast.Attribute) -> None:
        """Enhanced attribute access analysis for sandbox escape detection."""
        if not self._should_continue_analysis():
            return
            
        attr_name = node.attr
        
        # Dangerous dunder attributes
        if self._dangerous_attrs.match(attr_name):
            # Special handling for critical sandbox escape vectors
            if attr_name in ['__code__', '__globals__', '__loader__', '__spec__']:
                self.add_violation(
                    'sandbox_escape_attempt',
                    f'Sandbox escape attempt via {attr_name}',
                    SecurityRisk.CRITICAL,
                    node,
                    attr_name
                )
            elif attr_name in ['__class__', '__bases__', '__mro__', '__subclasses__']:
                self.add_violation(
                    'class_introspection',
                    f'Class introspection detected: {attr_name}',
                    SecurityRisk.HIGH,
                    node,
                    attr_name
                )
            else:
                self.add_violation(
                    'dunder_attribute_access',
                    f'Access to special attribute: {attr_name}',
                    SecurityRisk.MEDIUM,
                    node,
                    attr_name
                )
        
        # Check for specific dangerous attribute patterns
        elif attr_name in ['gi_frame', 'gi_code', 'cr_frame', 'cr_code']:
            self.add_violation(
                'generator_frame_access',
                f'Generator/coroutine frame access: {attr_name}',
                SecurityRisk.HIGH,
                node,
                attr_name
            )
        
        self.generic_visit(node)

    def visit_FunctionDef(self, node: ast.FunctionDef) -> None:
        """Enhanced function definition analysis."""
        if not self._should_continue_analysis():
            return
            
        self.function_names.add(node.name)
        
        # Check for suspicious function names that could be security risks
        suspicious_names = {'eval', 'exec', 'compile', 'system', 'popen', 'shell', 'spawn'}
        if node.name.lower() in suspicious_names:
            self.add_violation(
                'suspicious_function_name',
                f'Suspicious function name: {node.name}',
                SecurityRisk.HIGH,  # Elevated severity
                node,
                node.name
            )
        
        self.generic_visit(node)

    def visit_For(self, node: ast.For) -> None:
        """Enhanced loop analysis for DoS prevention and resource exhaustion."""
        if not self._should_continue_analysis():
            return
            
        self.loop_nesting += 1
        
        # Deep nesting check (potential CPU bomb)
        if self.loop_nesting > self.max_loop_nesting:
            self.add_violation(
                'deep_loop_nesting',
                f'Deep loop nesting detected (level {self.loop_nesting})',
                SecurityRisk.MEDIUM,
                node
            )
        
        # Check for resource exhaustion in loop iterators
        self._check_loop_resource_exhaustion(node)
        
        self.generic_visit(node)
        self.loop_nesting -= 1
        
    def _check_loop_resource_exhaustion(self, node: ast.For) -> None:
        """Check for resource exhaustion patterns in loop iterators."""
        # Large range loops
        if isinstance(node.iter, ast.Call) and self._get_function_name(node.iter.func) == 'range':
            if node.iter.args and isinstance(node.iter.args[0], ast.Constant):
                if isinstance(node.iter.args[0].value, int) and node.iter.args[0].value > self.max_loop_iterations:
                    self.add_violation(
                        'large_loop',
                        f'Large iteration count: range({node.iter.args[0].value})',
                        SecurityRisk.HIGH,  # Increased severity
                        node
                    )
        
        # List/generator expressions that could consume memory
        elif isinstance(node.iter, (ast.ListComp, ast.GeneratorExp)):
            self.add_violation(
                'complex_iterator',
                'Complex iterator in loop (potential memory exhaustion)',
                SecurityRisk.MEDIUM,
                node
            )

    def visit_While(self, node: ast.While) -> None:
        """Enhanced while loop analysis for infinite loop detection."""
        if not self._should_continue_analysis():
            return
            
        self.loop_nesting += 1
        
        # Check for while True patterns (potential DoS)
        if isinstance(node.test, ast.Constant) and node.test.value is True:
            self.add_violation(
                'infinite_loop_pattern',
                'Infinite loop pattern detected: while True',
                SecurityRisk.HIGH,  # Elevated for sandbox security
                node,
                'while True:'
            )
        
        self.generic_visit(node)
        self.loop_nesting -= 1

    def visit_BinOp(self, node: ast.BinOp) -> None:
        """Enhanced binary operation analysis for resource exhaustion attacks."""
        if not self._should_continue_analysis():
            return
            
        # Power operations (exponential complexity)
        if isinstance(node.op, ast.Pow):
            if isinstance(node.right, ast.Constant) and isinstance(node.right.value, (int, float)):
                if node.right.value > self.max_exponentiation:
                    self.add_violation(
                        'large_exponentiation',
                        f'Large exponentiation detected (exponent: {node.right.value})',
                        SecurityRisk.HIGH,  # Increased severity
                        node
                    )
        
        # String/list multiplication (memory bombs)
        elif isinstance(node.op, ast.Mult):
            self._check_multiplication_bomb(node)
        
        self.generic_visit(node)
        
    def _check_multiplication_bomb(self, node: ast.BinOp) -> None:
        """Check for memory exhaustion via string/list multiplication."""
        # Check both operands for large constants
        left_large = (isinstance(node.left, ast.Constant) and 
                     isinstance(node.left.value, int) and 
                     node.left.value > self.max_string_repetition)
                     
        right_large = (isinstance(node.right, ast.Constant) and 
                      isinstance(node.right.value, int) and 
                      node.right.value > self.max_string_repetition)
        
        if left_large or right_large:
            multiplier = node.left.value if left_large else node.right.value
            self.add_violation(
                'memory_exhaustion_multiplication',
                f'Large multiplication detected (factor: {multiplier})',
                SecurityRisk.HIGH,
                node
            )

    def visit_ListComp(self, node: ast.ListComp) -> None:
        """Check list comprehensions for potential memory exhaustion."""
        if not self._should_continue_analysis():
            return
            
        # Check for nested comprehensions (memory multiplication)
        nested_count = sum(1 for generator in node.generators 
                          for if_clause in generator.ifs 
                          if isinstance(if_clause, (ast.ListComp, ast.SetComp, ast.DictComp)))
        
        if nested_count > 1:
            self.add_violation(
                'nested_comprehension',
                'Nested comprehensions detected (memory exhaustion risk)',
                SecurityRisk.MEDIUM,
                node
            )
        
        # Check for large range in comprehension
        for generator in node.generators:
            if isinstance(generator.iter, ast.Call):
                if self._get_function_name(generator.iter.func) == 'range':
                    self._check_large_range(generator.iter, 'comprehension_large_range')
        
        self.generic_visit(node)
        
    def visit_SetComp(self, node: ast.SetComp) -> None:
        """Check set comprehensions for memory issues."""
        if not self._should_continue_analysis():
            return
        # Reuse list comprehension logic
        self.visit_ListComp(node)
        
    def visit_DictComp(self, node: ast.DictComp) -> None:
        """Check dictionary comprehensions for memory issues."""
        if not self._should_continue_analysis():
            return
        # Reuse list comprehension logic
        self.visit_ListComp(node)

    def visit_ClassDef(self, node: ast.ClassDef) -> None:
        """Enhanced class definition analysis for sandbox security."""
        if not self._should_continue_analysis():
            return
            
        self.class_nesting += 1
        
        # Metaclass usage is dangerous in sandboxes
        for keyword in node.keywords:
            if keyword.arg == 'metaclass':
                self.add_violation(
                    'metaclass_manipulation',
                    f'Metaclass usage in class {node.name} (sandbox escape risk)',
                    SecurityRisk.CRITICAL,  # Elevated to critical
                    node,
                    f'class {node.name}(metaclass=...)'
                )
        
        # Suspicious class names
        suspicious_names = {'eval', 'exec', 'compile', 'system', 'shell', 'spawn', 'loader'}
        if node.name.lower() in suspicious_names:
            self.add_violation(
                'suspicious_class_name',
                f'Suspicious class name: {node.name}',
                SecurityRisk.HIGH,
                node,
                node.name
            )
        
        self.generic_visit(node)
        self.class_nesting -= 1
    
    def visit_JoinedStr(self, node: ast.JoinedStr) -> None:
        """Enhanced f-string security analysis for sandbox environments."""
        if not self._should_continue_analysis():
            return
            
        for value in node.values:
            if isinstance(value, ast.FormattedValue):
                # Critical: Code injection via f-strings
                if isinstance(value.value, ast.Call):
                    func_name = self._get_function_name(value.value.func)
                    if func_name in ['eval', 'exec', '__import__', 'compile']:
                        self.add_violation(
                            'fstring_code_injection',
                            f'F-string code injection: {func_name}',
                            SecurityRisk.CRITICAL,
                            node,
                            f'f"...{{{func_name}(...)}}..."'
                        )
                # Dangerous attribute access in f-strings
                elif isinstance(value.value, ast.Attribute):
                    if self._dangerous_attrs.match(value.value.attr):
                        self.add_violation(
                            'fstring_dangerous_attribute',
                            f'F-string dangerous attribute access: {value.value.attr}',
                            SecurityRisk.HIGH,
                            node,
                            value.value.attr
                        )
                # Check for potential format string attacks
                elif isinstance(value.value, ast.Subscript):
                    self.add_violation(
                        'fstring_subscript_access',
                        'F-string with subscript access (potential data leakage)',
                        SecurityRisk.MEDIUM,
                        node
                    )
        self.generic_visit(node)
    
    def visit_Lambda(self, node: ast.Lambda) -> None:
        """Enhanced lambda expression security analysis."""
        if not self._should_continue_analysis():
            return
            
        # Critical: Lambda with dangerous function calls
        if isinstance(node.body, ast.Call):
            func_name = self._get_function_name(node.body.func)
            if func_name in ['eval', 'exec', 'compile', '__import__']:
                self.add_violation(
                    'lambda_code_injection',
                    f'Lambda code injection: {func_name}',
                    SecurityRisk.CRITICAL,  # Elevated to critical
                    node,
                    f'lambda: {func_name}(...)'
                )
        # Lambda with dangerous attribute access
        elif isinstance(node.body, ast.Attribute):
            if self._dangerous_attrs.match(node.body.attr):
                self.add_violation(
                    'lambda_dangerous_attribute',
                    f'Lambda dangerous attribute access: {node.body.attr}',
                    SecurityRisk.HIGH,
                    node,
                    node.body.attr
                )
        self.generic_visit(node)
        
    def visit_Subscript(self, node: ast.Subscript) -> None:
        """Check subscript operations for potential security issues."""
        if not self._should_continue_analysis():
            return
            
        # Check for dangerous subscript patterns that could access internals
        if isinstance(node.value, ast.Call):
            func_name = self._get_function_name(node.value.func)
            if func_name in ['globals', 'locals', 'vars']:
                self.add_violation(
                    'namespace_subscript_access',
                    f'Subscript access to namespace: {func_name}()[...]',
                    SecurityRisk.HIGH,
                    node,
                    f'{func_name}()[subscript]'
                )
        
        self.generic_visit(node)
        
    def visit_With(self, node: ast.With) -> None:
        """Check context managers for potential security issues."""
        if not self._should_continue_analysis():
            return
            
        # Check for dangerous context manager usage
        for item in node.items:
            if isinstance(item.context_expr, ast.Call):
                func_name = self._get_function_name(item.context_expr.func)
                if func_name == 'open':
                    # File operations in context managers
                    self.add_violation(
                        'context_manager_file_access',
                        'File access via context manager',
                        SecurityRisk.HIGH,
                        item.context_expr,
                        'with open(...) as f:'
                    )
        
        self.generic_visit(node)
        
    def visit_Global(self, node: ast.Global) -> None:
        """Check global variable declarations."""
        if not self._should_continue_analysis():
            return
            
        # Global variable manipulation can be dangerous in sandboxes
        for name in node.names:
            if name.startswith('__') or name in ['exit', 'quit', 'help', 'license', 'credits']:
                self.add_violation(
                    'dangerous_global_declaration',
                    f'Dangerous global variable declaration: {name}',
                    SecurityRisk.MEDIUM,
                    node,
                    f'global {name}'
                )
        
        self.generic_visit(node)
        
    def visit_Nonlocal(self, node: ast.Nonlocal) -> None:
        """Check nonlocal variable declarations."""
        if not self._should_continue_analysis():
            return
            
        # Nonlocal with dangerous names
        for name in node.names:
            if name.startswith('__'):
                self.add_violation(
                    'dangerous_nonlocal_declaration',
                    f'Dangerous nonlocal variable declaration: {name}',
                    SecurityRisk.MEDIUM,
                    node,
                    f'nonlocal {name}'
                )
        
        self.generic_visit(node)
    
    def visit_Try(self, node: ast.Try) -> None:
        """Enhanced exception handling analysis."""
        if not self._should_continue_analysis():
            return
            
        for handler in node.handlers:
            # Bare except clauses (security risk)
            if handler.type is None:
                self.add_violation(
                    'bare_except_clause',
                    'Bare except clause detected - can hide critical errors',
                    SecurityRisk.MEDIUM,
                    handler,
                    'except:'
                )
            # Check for dangerous exception handling patterns
            elif isinstance(handler.type, ast.Name) and handler.type.id in ['SystemExit', 'KeyboardInterrupt']:
                self.add_violation(
                    'dangerous_exception_handling',
                    f'Catching {handler.type.id} can interfere with sandbox control',
                    SecurityRisk.HIGH,
                    handler,
                    f'except {handler.type.id}:'
                )
        self.generic_visit(node)
    
    def visit_AsyncFunctionDef(self, node: ast.AsyncFunctionDef) -> None:
        """Check async functions for potential security issues."""
        if not self._should_continue_analysis():
            return
            
        # Check for suspicious async function names
        suspicious_names = ['eval', 'exec', 'compile', 'system', 'shell', 'spawn']
        if node.name.lower() in suspicious_names:
            self.add_violation(
                'suspicious_async_function',
                f'Suspicious async function name: {node.name}',
                SecurityRisk.HIGH,  # Increased severity
                node,
                node.name
            )
        self.generic_visit(node)
        
    def visit_Await(self, node: ast.Await) -> None:
        """Check await expressions for async security issues."""
        if not self._should_continue_analysis():
            return
            
        # Check for dangerous awaited calls
        if isinstance(node.value, ast.Call):
            func_name = self._get_function_name(node.value.func)
            if func_name in ['eval', 'exec', 'compile']:
                self.add_violation(
                    'async_code_execution',
                    f'Async code execution detected: await {func_name}()',
                    SecurityRisk.CRITICAL,
                    node,
                    f'await {func_name}'
                )
        self.generic_visit(node)

    def generic_visit(self, node: ast.AST) -> None:
        """Optimized generic visitor with early termination and violation limits."""
        # Multiple early termination conditions for performance
        if (self.critical_found and self.early_termination) or len(self.violations) >= self.max_violations:
            return
        super().generic_visit(node)

    def _get_function_name(self, node: ast.AST) -> Optional[str]:
        """Optimized function name extraction."""
        # Fast path for common cases
        if isinstance(node, ast.Name):
            return node.id
        elif isinstance(node, ast.Attribute):
            return node.attr
        # Avoid recursion for performance
        elif isinstance(node, ast.Call) and isinstance(node.func, ast.Name):
            return node.func.id
        elif isinstance(node, ast.Call) and isinstance(node.func, ast.Attribute):
            return node.func.attr
        return None
        
    def _should_continue_analysis(self) -> bool:
        """Centralized check for whether to continue analysis."""
        return not (
            (self.critical_found and self.early_termination) or 
            len(self.violations) >= self.max_violations
        )

    def validate(self, code: str) -> ValidationResult:
        """Optimized main validation method with comprehensive security checks."""
        start_time = time.time()
        timeout_seconds = self.config.get('validation_timeout', 10)
        
        def timeout_handler(signum, frame):
            raise TimeoutError("Code validation timeout exceeded")
        
        try:
            # Set timeout if supported (Unix systems only)
            if hasattr(signal, 'SIGALRM'):
                signal.signal(signal.SIGALRM, timeout_handler)
                signal.alarm(timeout_seconds)
            
            # Pre-validation checks for quick rejection
            if not self._pre_validate(code):
                return self._create_result(start_time)
            
            # Parse and analyze AST
            tree = ast.parse(code)
            self.visit(tree)
            
            return self._create_result(start_time)
            
        except TimeoutError:
            return self._create_timeout_result(start_time, timeout_seconds)
        except SyntaxError as e:
            return self._create_syntax_error_result(start_time, e)
        except Exception as e:
            return self._create_parse_error_result(start_time, e)
        finally:
            # Clean up timeout signal
            if hasattr(signal, 'SIGALRM'):
                signal.alarm(0)
                
    def _pre_validate(self, code: str) -> bool:
        """Fast pre-validation checks for obvious violations."""
        code_size = len(code.encode('utf-8'))


        # Enhanced pattern matching for sandbox-specific threats
        dangerous_patterns = [
            # Code injection patterns
            (r'\beval\s*\(', 'eval() detected'),
            (r'\bexec\s*\(', 'exec() detected'),
            (r'\b__import__\s*\(', '__import__() detected'),
            (r'\bcompile\s*\(', 'compile() detected'),
            # Sandbox escape patterns
            (r'\b__subclasses__\s*\(', 'Class introspection detected'),
            (r'\b__globals__\b', 'Global namespace access detected'),
            (r'\b__code__\b', 'Code object access detected'),
            (r'\b__loader__\b', 'Module loader access detected'),
            # Resource exhaustion patterns
            (r'\[.*\]\s*\*\s*\d{6,}', 'Large list multiplication detected'),
            (r'\".*\"\s*\*\s*\d{6,}', 'Large string multiplication detected'),
            (r'\brange\s*\(\s*\d{7,}', 'Large range detected'),
            # Network/system access patterns
            (r'\bsocket\s*\(', 'Socket creation detected'),
            (r'\bsubprocess\s*\.', 'Subprocess access detected'),
            (r'\bos\s*\.', 'OS module access detected')
        ]
        
        for pattern, description in dangerous_patterns:
            if re.search(pattern, code):
                self.add_violation(
                    'dangerous_pattern_detected',
                    description,
                    SecurityRisk.CRITICAL
                )
                if self.early_termination:
                    return False  # Stop processing
        
        return True
        
    def _create_result(self, start_time: float) -> ValidationResult:
        """Create validation result from current state."""
        duration_ms = (time.time() - start_time) * 1000
        
        # Determine overall risk level
        max_risk = SecurityRisk.SAFE
        for violation in self.violations:
            if violation.risk > max_risk:
                max_risk = SecurityRisk(violation.risk)
        
        # Code is safe if no critical violations
        safe = max_risk < SecurityRisk.CRITICAL
        
        return ValidationResult(
            safe=safe,
            risk=max_risk.value,
            violations=[asdict(v) for v in self.violations],
            duration_ms=duration_ms
        )
        
    def _create_timeout_result(self, start_time: float, timeout_seconds: int) -> ValidationResult:
        """Create result for timeout scenarios."""
        return ValidationResult(
            safe=False,
            risk=SecurityRisk.CRITICAL.value,  # Timeout is critical
            violations=[{
                'type': 'validation_timeout',
                'description': f'Validation timeout after {timeout_seconds}s (suspicious code complexity)',
                'risk': SecurityRisk.CRITICAL.value,
                'line': None,
                'column': None,
                'pattern': None,
                'context': None
            }],
            duration_ms=(time.time() - start_time) * 1000
        )
        
    def _create_syntax_error_result(self, start_time: float, e: SyntaxError) -> ValidationResult:
        """Create result for syntax errors."""
        violation = SecurityViolation(
            type='syntax_error',
            description=f'Invalid Python syntax: {str(e)}',
            risk=SecurityRisk.HIGH.value,
            line=e.lineno,
            column=e.offset
        )
        
        return ValidationResult(
            safe=False,
            risk=SecurityRisk.HIGH.value,
            violations=[asdict(violation)],
            duration_ms=(time.time() - start_time) * 1000
        )
        
    def _create_parse_error_result(self, start_time: float, e: Exception) -> ValidationResult:
        """Create result for parsing errors."""
        violation = SecurityViolation(
            type='parse_error',
            description=f'Code parsing error: {str(e)}',
            risk=SecurityRisk.CRITICAL.value  # Parse errors in sandbox are suspicious
        )
        
        return ValidationResult(
            safe=False,
            risk=SecurityRisk.CRITICAL.value,
            violations=[asdict(violation)],
            duration_ms=(time.time() - start_time) * 1000
        )


def validate_config(config: dict[str, Any]) -> dict[str, Any]:
    """Validate configuration - config.yaml is single source of truth."""
    # Default values for missing configuration sections
    defaults = {
        # Network request defaults
        'network_requests': {
            'allow': True,
            'blocked_when_disabled': [],
            'risk_level': 'critical'
        },
        # Resource limits defaults
        'resource_limits': {
            'max_loop_iterations': 1000000,
            'max_memory_allocation': 100000000,
            'max_string_repetition': 10000000,
            'max_exponentiation': 1000,
            'max_loop_nesting': 3,
            'max_comprehension_nesting': 2
        },
        # Validator behavior defaults
        'validator': {
            'early_termination': True,
            'max_violations': 50,
            'validation_timeout': 10,
            'max_code_size': 1048576
        }
    }
    
    # Start with user config, add defaults for missing sections
    validated_config = config.copy()
    
    # Add missing configuration sections
    for section, default_values in defaults.items():
        if section not in validated_config:
            validated_config[section] = default_values.copy()
        else:
            # Add missing keys within existing sections
            for key, default_value in default_values.items():
                if key not in validated_config[section]:
                    validated_config[section][key] = default_value
    
    # Ensure blocked_imports is a list
    if 'blocked_imports' not in validated_config:
        validated_config['blocked_imports'] = []
    elif not isinstance(validated_config['blocked_imports'], list):
        validated_config['blocked_imports'] = []
    
    # Validate numeric constraints in validator section
    validator_config = validated_config.get('validator', {})
    if validator_config.get('max_violations', 0) <= 0:
        validator_config['max_violations'] = 50
    if validator_config.get('validation_timeout', 0) <= 0:
        validator_config['validation_timeout'] = 10

    # Validate network request risk level
    network_config = validated_config.get('network_requests', {})
    if network_config.get('risk_level', '').lower() not in ['safe', 'low', 'medium', 'high', 'critical']:
        network_config['risk_level'] = 'critical'
    
    return validated_config


def load_config() -> dict[str, Any]:
    """Optimized configuration loading with better error handling."""
    config_env = os.getenv('VALIDATOR_CONFIG', '')
    if not config_env:
        print("ERROR: VALIDATOR_CONFIG environment variable not set", file=sys.stderr)
        sys.exit(1)
    
    try:
        # Efficient base64 decode and JSON parse
        config_bytes = base64.b64decode(config_env, validate=True)
        config_json = config_bytes.decode('utf-8')
        raw_config = json.loads(config_json)
        
        if not isinstance(raw_config, dict):
            raise ValueError("Configuration must be a JSON object")
        
        # Validate and return sanitized configuration
        return validate_config(raw_config)
        
    except (binascii.Error, UnicodeDecodeError) as e:
        print(f"ERROR: Invalid base64 configuration: {e}", file=sys.stderr)
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"ERROR: Invalid JSON configuration: {e}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"ERROR: Configuration validation failed: {e}", file=sys.stderr)
        sys.exit(1)


def main() -> None:
    """Optimized main entry point with improved error handling."""
    start_time = time.time()
    
    try:
        # Load and validate configuration
        config = load_config()
        
        # Read code from stdin with size limit check
        code = sys.stdin.read()
        
        # Quick validation for empty code
        if not code.strip():
            result = ValidationResult(
                safe=True,
                risk=SecurityRisk.SAFE.value,
                violations=[],
                duration_ms=(time.time() - start_time) * 1000
            )
        else:
            # Perform full validation
            validator = SecurityValidator(config)
            result = validator.validate(code)
        
        # Output result as compact JSON for performance
        print(json.dumps(asdict(result), separators=(',', ':')))
        
    except (KeyboardInterrupt, SystemExit):
        # Handle interruption gracefully
        error_result = _create_error_result(
            'validation_interrupted', 
            'Validation interrupted by system',
            SecurityRisk.CRITICAL,
            start_time
        )
        print(json.dumps(asdict(error_result), separators=(',', ':')))
        sys.exit(1)
        
    except Exception as e:
        # Handle unexpected errors with detailed logging to stderr
        print(f"ERROR: Unexpected validator error: {e}", file=sys.stderr)
        error_result = _create_error_result(
            'validator_error',
            f'Validator error: {str(e)}',
            SecurityRisk.CRITICAL,  # All unexpected errors are critical
            start_time
        )
        print(json.dumps(asdict(error_result), separators=(',', ':')))
        sys.exit(1)
        

def _create_error_result(error_type: str, description: str, risk: SecurityRisk, start_time: float) -> ValidationResult:
    """Helper function to create standardized error results."""
    return ValidationResult(
        safe=False,
        risk=risk.value,
        violations=[{
            'type': error_type,
            'description': description,
            'risk': risk.value,
            'line': None,
            'column': None,
            'pattern': None,
            'context': None
        }],
        duration_ms=(time.time() - start_time) * 1000
    )


if __name__ == '__main__':
    main()