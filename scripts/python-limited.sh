#!/bin/sh
# Python execution wrapper with resource limits
# This script applies ulimit restrictions before executing Python code

# Set memory limits (virtual memory in KB)
# Default: 102400 KB = 100 MB
ulimit -v ${MEMORY_LIMIT_KB:-102400}

# Set maximum file size for output (KB)
# Prevents processes from creating huge output files
ulimit -f ${FILE_SIZE_LIMIT:-10240}

# Set maximum number of processes (if supported)
# Prevents fork bombs and excessive process creation
ulimit -u ${MAX_PROCESSES:-10} 2>/dev/null || true

# Set maximum number of open files
# Prevents file descriptor exhaustion
ulimit -n ${MAX_OPEN_FILES:-100}

# Set maximum stack size (KB)
# Prevents stack overflow attacks
ulimit -s ${MAX_STACK_SIZE:-8192}

# Replace current process with Python (no extra shell layer)
# This ensures ulimits apply directly to the Python process
exec python3 "$@"