package main

import (
	"errors"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"code-sandbox/internal/config"
	"code-sandbox/internal/executor"
	"code-sandbox/internal/handler"
	"code-sandbox/internal/logger"
	"code-sandbox/internal/middleware"

	"go.uber.org/zap"
)

func main() {
	// Load configuration from YAML file
	configPath := os.Getenv("CONFIG_PATH")
	if configPath == "" {
		configPath = "/app/config.yaml" // Default path in container
	}
	cfg, err := config.LoadConfig(configPath)
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize global logger
	logConfig := &logger.LogConfig{
		Level:      cfg.LogConfig.Level,
		FilePath:   cfg.LogConfig.FilePath,
		MaxSize:    cfg.LogConfig.MaxSize,
		MaxAge:     cfg.LogConfig.MaxAge,
		MaxBackups: cfg.LogConfig.MaxBackups,
		Compress:   cfg.LogConfig.Compress,
		Console:    cfg.LogConfig.Console,
		FileOutput: cfg.LogConfig.FileOutput,
		Async:      cfg.LogConfig.Async,
		BufferSize: cfg.LogConfig.BufferSize,
	}

	if err := logger.Initialize(logConfig); err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}
	defer func() {
		if err := logger.Sync(); err != nil {
			log.Printf("Failed to sync logger: %v", err)
		}
	}()

	// Create execution pool
	pool := executor.NewPool(cfg)

	// Create global rate limiter
	rateLimiter := middleware.NewGlobalRateLimiter(cfg.RateLimitRPS)

	// Create handlers
	executeHandler := handler.NewExecuteHandler(pool, cfg)
	healthHandler := handler.NewHealthHandler(pool)

	// Create middlewares
	rateLimitMiddleware := middleware.RateLimitMiddleware(rateLimiter)
	authMiddleware := middleware.AuthMiddleware(cfg.AuthToken)

	// Setup routes - chain middlewares:  auth → rate limit → handler
	http.HandleFunc("/execute",
		authMiddleware(
			rateLimitMiddleware(
				executeHandler.Handle)))

	// Health endpoints don't need rate limiting or auth
	http.HandleFunc("/status", healthHandler.HandleStatus)
	http.HandleFunc("/health", healthHandler.HandleHealth)

	// Start HTTP server
	server := &http.Server{
		Addr:           ":" + cfg.ServerPort,
		ReadTimeout:    10 * time.Second,
		WriteTimeout:   60 * time.Second, // Allow long-running executions
		MaxHeaderBytes: 1 << 20,          // 1MB
	}

	// Graceful shutdown handling
	go func() {
		sigChan := make(chan os.Signal, 1)
		signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
		<-sigChan

		logger.Info("Received shutdown signal")
		pool.Shutdown()
		if err := server.Close(); err != nil {
			logger.Error("Failed to close server", zap.Error(err))
		}
	}()

	logger.Info("Server starting", zap.String("port", cfg.ServerPort))
	if err := server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
		logger.Fatal("Server failed to start", zap.Error(err))
	}

	logger.Info("Server stopped")
}
