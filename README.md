# Code Executor - Python代码执行服务

基于Go 1.25.0和Worker Pool模式的高性能Python代码执行服务，支持Token认证、Base64传输和Docker部署。

## 功能特性

- ✅ **Token认证**: Bearer Token认证，确保API安全访问
- ✅ **Base64传输**: 代码通过Base64编码传输，支持特殊字符和多行代码
- ✅ **高并发**: Worker Pool模式，支持配置的并发执行
- ✅ **Docker部署**: 包含Go 1.25.0和Python 3.13的完整运行环境
- ✅ **RESTful API**: 安全的HTTP接口接收和执行Python代码
- ✅ **超时控制**: 可配置的代码执行超时机制
- ✅ **资源管理**: 自动清理临时文件，防止资源泄露
- ✅ **健康检查**: 内置健康检查和状态监控接口
- ✅ **优雅关闭**: 支持信号处理和优雅停机
- ✅ **预装依赖**: Docker镜像预装常用Python库，无需额外安装

## 预装Python依赖包

Docker镜像已预装以下常用Python库，用户代码可直接导入使用：

### 📊 数据处理和分析
- **numpy** (>=1.26.0) - 数值计算基础库
- **pandas** (>=2.1.0) - 数据分析和处理

### 📈 数据可视化
- **matplotlib** (>=3.8.0) - 数据可视化库

### 🌐 网络和Web
- **requests** (>=2.31.0) - HTTP请求库
- **beautifulsoup4** (>=4.12.2) - HTML/XML解析

### 🛠️ 实用工具
- **pyyaml** (>=6.0.1) - YAML文件处理
- **pillow** (>=10.1.0) - 图像处理库
- **python-dateutil** (>=2.8.2) - 日期处理
- **psutil** (>=5.9.6) - 系统信息监控
- **ujson** (>=5.8.0) - 高性能JSON处理

### 测试预装包
```bash
# 使用Go测试工具验证所有包是否正常
cd test
go run api_test_main.go -test packages

# 或测试所有功能（包含预装包测试）
go run api_test_main.go
```

## 项目结构

```
code-sandbox/
├── main.go                    # HTTP服务器主程序
├── executor.go                # Worker Pool和执行器实现
├── models.go                  # 数据结构定义
├── requirements.txt           # Python依赖包配置
├── go.mod                     # Go模块配置
├── go.sum                     # 依赖校验文件
├── Dockerfile                 # Docker镜像配置
├── docker-compose.yml         # Docker编排配置
└── test/
    ├── api_test_main.go       # Go语言API完整测试工具
    ├── api_test.sh            # Shell脚本测试工具
    └── test_python_packages.py # Python包验证脚本
```

## 快速开始

### 1. 使用Docker Compose启动（推荐）

```bash
# 构建并启动服务
docker-compose up --build

# 后台运行
docker-compose up -d --build
```

### 2. 手动构建Docker镜像

```bash
# 构建镜像
docker build -t code-sandbox .

# 运行容器
docker run -p 8080:8080 code-sandbox
```

### 3. 本地开发运行

```bash
# 安装依赖
go mod tidy

# 确保系统安装了Python 3.13
python3 --version

# 运行服务
go run *.go
```

## API接口

### 执行Python代码

**POST** `/execute`

**请求示例:**
```bash
# 1. 对Python代码进行Base64编码
CODE='print("Hello, World!")
result = 2 + 3  
print(f"Result: {result}")
print("特殊字符测试: @#$%^&*()")'

CODE_BASE64=$(echo -n "$CODE" | base64)

# 2. 发送认证请求
curl -X POST http://localhost:8080/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-secret-token-here" \
  -d "{\"code_base64\": \"$CODE_BASE64\"}"
```

**响应示例:**
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "output": "Hello, World!\nResult: 5\n特殊字符测试: @#$%^&*()\n",
  "error": "",
  "success": true,
  "exec_time_ms": 45
}
```

**使用预装Python库示例:**
```bash
# 数据分析示例 - 使用numpy和pandas
CODE_DATA='import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

# 创建数据
data = np.random.randn(100)
df = pd.DataFrame({
    "values": data,
    "squared": data ** 2
})

# 统计分析
print(f"数据统计:")
print(f"平均值: {df[\"values\"].mean():.3f}")
print(f"标准差: {df[\"values\"].std():.3f}")
print(f"最大值: {df[\"values\"].max():.3f}")
print(f"最小值: {df[\"values\"].min():.3f}")

# HTTP请求示例
import requests
print(f"Requests库版本: {requests.__version__}")

print("✅ 数据分析完成！")'

CODE_BASE64=$(echo -n "$CODE_DATA" | base64)

curl -X POST http://localhost:8080/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-secret-token-here" \
  -d "{\"code_base64\": \"$CODE_BASE64\"}"
```

**错误响应:**
```bash
# Token缺失或无效
HTTP/1.1 401 Unauthorized
{"error": "Unauthorized"}

# Base64编码无效
HTTP/1.1 400 Bad Request
{"error": "Invalid Base64 encoding"}

# code_base64字段缺失
HTTP/1.1 400 Bad Request
{"error": "code_base64 cannot be empty"}
```

### 服务状态

**GET** `/status`

```bash
curl http://localhost:8080/status
```

**响应示例:**
```json
{
  "workers": 10,
  "queue_size": 100,
  "timeout": "30s",
  "timestamp": "2024-08-30T12:00:00Z"
}
```

### 健康检查

**GET** `/health`

```bash
curl http://localhost:8080/health
```

## 配置参数

通过环境变量配置服务参数：

| 环境变量 | 默认值 | 说明 |
|---------|-------|------|
| `AUTH_TOKEN` | `secret-token-123456` | API认证Token |
| `SERVER_PORT` | `8080` | HTTP服务端口 |
| `WORKER_COUNT` | `10` | Worker协程数量 |
| `QUEUE_SIZE` | `100` | 任务队列大小 |
| `EXEC_TIMEOUT` | `30s` | 代码执行超时时间 |

**Docker Compose配置示例:**
```yaml
environment:
  - AUTH_TOKEN=your-production-secret-token
  - SERVER_PORT=8080
  - WORKER_COUNT=20
  - QUEUE_SIZE=200
  - EXEC_TIMEOUT=60s
```

## 功能测试

### 使用Go测试工具（推荐）

```bash
# 确保服务在运行
docker-compose up -d

# 运行完整测试套件
cd test
go run api_test_main.go

# 运行特定测试类型
go run api_test_main.go -test basic      # 基础功能
go run api_test_main.go -test packages   # 预装包
go run api_test_main.go -test auth       # 认证功能
go run api_test_main.go -test perf       # 性能测试
go run api_test_main.go -test errors     # 错误处理
go run api_test_main.go -test status     # 服务状态

# 连接远程服务器测试
go run api_test_main.go -url http://remote:8080 -token custom-token
```

### 使用Shell测试工具

```bash
# 运行Shell脚本测试
./api_test.sh

# 自定义配置
./api_test.sh -u http://remote:8080 -t custom-token
```

### Go测试结果示例

```
🚀 开始 Code Executor API 完整测试
🔗 连接到: http://localhost:8080
🔑 使用Token: secret-t...
============================================================
🏥 测试服务状态...
  ✅ 健康检查通过: OK
  ✅ 状态接口正常:
      Workers: 10
      Queue Size: 100
      Timeout: 30s

🔧 测试基础功能...
  ✅ 简单输出: 0.045s (执行: 12ms)
  ✅ 数学计算: 0.038s (执行: 8ms)
  ✅ 字符串操作: 0.041s (执行: 9ms)

📦 测试预装Python包...
  ✅ NumPy数值计算: 0.156s (执行: 98ms)
  ✅ Pandas数据分析: 0.234s (执行: 187ms)
  ✅ 综合包测试: 0.298s (执行: 245ms)

⚡ 测试性能和并发...
  ✅ 复杂计算性能: 0.445s (执行: 388ms)
  ✅ 并发测试: 10/10 成功, 总用时: 0.156s

============================================================
📊 测试报告
============================================================
总测试数: 18
成功: 18
失败: 0
成功率: 100.0%
总用时: 3.245秒
平均响应时间: 0.180秒

🎉 测试结果: 优秀! 系统运行完美
```

## Worker Pool架构

```
                    HTTP请求
                       ↓
                  API Handler
                       ↓
    Job Queue ← ← ← 生成Job对象
        ↓
    Worker Pool (10个Worker)
        ↓
 空闲Worker获取Job
        ↓
   执行Python代码
        ↓
    返回结果 → → → 响应客户端
```

### 核心组件

1. **Job Queue**: 缓冲队列，默认容量100个任务
2. **Worker Pool**: 10个长期运行的goroutine处理任务
3. **Result Channel**: 用于Worker向API Handler返回执行结果
4. **Timeout Control**: 每个任务都有独立的超时控制

## 安全特性

✅ **已实现的安全功能：**
- **Token认证**: Bearer Token验证，防止未授权访问
- **Base64编码**: 避免代码注入和JSON转义问题
- **输入验证**: 严格的请求参数验证和错误处理
- **日志记录**: Token掩码处理，防止敏感信息泄露
- **超时控制**: 防止恶意代码长时间占用资源

⚠️ **生产环境建议添加：**
- 代码沙盒执行环境
- 资源使用限制（CPU、内存、磁盘）
- 恶意代码检测和过滤
- 请求频率限制和IP白名单
- SSL/TLS加密传输
- 审计日志和监控告警

## 开发计划

- [ ] 添加代码执行沙盒
- [ ] 实现资源使用监控和限制
- [ ] 添加请求认证和授权
- [ ] 支持更多编程语言
- [ ] 添加执行结果缓存
- [ ] 实现分布式部署