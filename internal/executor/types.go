package executor

import "time"

// ExecuteRequest represents a code execution request
type ExecuteRequest struct {
	CodeBase64 string `json:"code_base64"`
}

// ExecuteResponse represents a code execution response
type ExecuteResponse struct {
	ID         string `json:"id"`
	Stdout     string `json:"stdout"`
	Stderr     string `json:"stderr"`
	Error      string `json:"error"`
	Success    bool   `json:"success"`
	ExecTimeMs int64  `json:"exec_time_ms"`
	Warning    string `json:"warning,omitempty"`
}

// Job represents a pending execution task
type Job struct {
	ID       string
	Code     string
	ResultCh chan<- Result
}

// Result represents the result of task execution
type Result struct {
	ID       string
	Stdout   string
	Stderr   string
	Error    string
	Success  bool
	ExecTime time.Duration
}
