package executor

import (
	"context"
	"errors"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"syscall"
	"time"

	"code-sandbox/internal/config"
	"code-sandbox/internal/logger"

	"go.uber.org/zap"
)

// Executor handles Python code execution with security validation
type Executor struct {
	config    *config.Config
	validator *CodeValidator
}

// NewExecutor creates a new code executor with security validation
func NewExecutor(cfg *config.Config) *Executor {
	return &Executor{
		config:    cfg,
		validator: NewCodeValidator(&cfg.SecurityConfig),
	}
}

// Execute executes Python code and returns the result with proper context handling
func (e *Executor) Execute(parentCtx context.Context, job Job) Result {
	startTime := time.Now()

	result := Result{
		ID:      job.ID,
		Success: false,
	}

	// Create child context with timeout from parent context for proper cancellation chain
	ctx, cancel := context.WithTimeout(parentCtx, e.config.ExecTimeout)
	defer cancel()

	logger.WithJobID(job.ID).Info("Processing job")

	// SECURITY VALIDATION: Analyze code before execution
	validationResult := e.validator.ValidateCode(job.Code)

	// Log security findings
	if len(validationResult.Violations) > 0 {
		logger.WithJobID(job.ID).Warn("Security analysis found violations",
			zap.Int("violations_count", len(validationResult.Violations)),
			zap.String("risk_level", validationResult.Risk.GetRiskDescription()))
		for i, violation := range validationResult.Violations {
			logger.WithJobID(job.ID).Warn("Security violation detected",
				zap.Int("violation_number", i+1),
				zap.String("type", violation.Type),
				zap.String("description", violation.Description),
				zap.Int("line", violation.Line))
		}
	}

	// Block execution for high-risk code
	if validationResult.ShouldBlock() {
		result.Error = fmt.Sprintf("Code execution blocked due to security policy violations (Risk: %s)",
			validationResult.Risk.GetRiskDescription())
		result.ExecTime = time.Since(startTime)
		logger.WithJobID(job.ID).Error("Job blocked due to security violations", zap.String("reason", result.Error))
		return result
	}

	logger.WithJobID(job.ID).Info("Security validation passed",
		zap.String("risk_level", validationResult.Risk.GetRiskDescription()),
		zap.Duration("validation_duration", validationResult.Duration))

	// Create temporary execution directory for isolation
	tmpDir := filepath.Join(os.TempDir(), fmt.Sprintf("pyexec_%s", job.ID))

	// Ensure cleanup (even if panic occurs)
	defer func() {
		if err := os.RemoveAll(tmpDir); err != nil {
			logger.Warn("Failed to cleanup temporary directory", zap.String("temp_dir", tmpDir), zap.Error(err))
		}
	}()

	// Create directory with strict permissions (only owner can access)
	if err := os.MkdirAll(tmpDir, 0700); err != nil {
		result.Error = fmt.Sprintf("Failed to create temp directory: %v", err)
		result.ExecTime = time.Since(startTime)
		logger.WithJobID(job.ID).Error("Job failed during temp directory creation", zap.String("error", result.Error))
		return result
	}

	// Write user code to main.py file
	codePath := filepath.Join(tmpDir, "main.py")
	if err := os.WriteFile(codePath, []byte(job.Code), 0600); err != nil {
		result.Error = fmt.Sprintf("Failed to write code file: %v", err)
		result.ExecTime = time.Since(startTime)
		logger.WithJobID(job.ID).Error("Job failed during code file writing", zap.String("error", result.Error))
		return result
	}

	// Create secure Python command using resource-limited wrapper
	wrapperPath := "/app/scripts/python-limited.sh"
	if _, err := exec.LookPath(wrapperPath); err != nil {
		// Fallback to direct python3 for local testing (resource limits disabled)
		wrapperPath = "python3"
	}

	// Execute Python with optimized mode, reading from file instead of stdin
	cmd := exec.CommandContext(ctx, wrapperPath, "-O", "main.py")
	cmd.Dir = tmpDir // Set working directory to temp directory

	// Set resource limit environment variables
	cmd.Env = []string{
		fmt.Sprintf("MEMORY_LIMIT_KB=%d", e.config.MemoryLimitMB*1024),
		fmt.Sprintf("FILE_SIZE_LIMIT=%d", e.config.FileSizeLimitKB),
		fmt.Sprintf("MAX_PROCESSES=%d", e.config.MaxProcesses),
		fmt.Sprintf("MAX_OPEN_FILES=%d", e.config.MaxOpenFiles),
		fmt.Sprintf("MAX_STACK_SIZE=%d", e.config.MaxStackSizeKB),
		"PATH=/usr/local/bin:/usr/bin:/bin",
		"PYTHONUNBUFFERED=1", // Real-time output
		"PYTHONOPTIMIZE=1",   // Use optimized mode to read .opt-1.pyc files
	}

	// Apply process isolation (Linux only)
	if runtime.GOOS == "linux" {
		cmd.SysProcAttr = &syscall.SysProcAttr{
			Setpgid: true, // Create new process group for better isolation
		}

		if wrapperPath == "/app/scripts/python-limited.sh" {
			logger.WithJobID(job.ID).Info("Temp directory isolation with resource limits",
				zap.String("temp_dir", tmpDir),
				zap.Int("memory_limit_mb", e.config.MemoryLimitMB),
				zap.Int("file_size_limit_kb", e.config.FileSizeLimitKB))
		} else {
			logger.WithJobID(job.ID).Info("Temp directory isolation (ulimit disabled)", zap.String("temp_dir", tmpDir))
		}
	} else {
		logger.WithJobID(job.ID).Info("Temp directory isolation on platform",
			zap.String("platform", runtime.GOOS),
			zap.String("temp_dir", tmpDir))
	}

	// Use optimized streaming runner
	runner := NewStreamRunner(cmd, ctx)
	streamResult := runner.Execute()

	result.ExecTime = time.Since(startTime)

	// Handle execution results
	if streamResult.HasError() {
		if streamResult.Error != nil {
			// System error (timeout, process failure, etc.)
			if runner.IsTimeout() || errors.Is(ctx.Err(), context.DeadlineExceeded) {
				result.Error = fmt.Sprintf("Python代码执行超时 (限制: %v)", e.config.ExecTimeout)
			} else {
				result.Error = fmt.Sprintf("执行失败: %v", streamResult.Error)
			}
		} else {
			// Python exited with non-zero code but no system error
			result.Error = fmt.Sprintf("Python执行错误:\n%s", strings.TrimSpace(string(streamResult.Stderr)))
		}
		logger.WithJobID(job.ID).Error("Job execution failed", zap.String("error", result.Error))
	} else {
		// Success case
		result.Success = true
	}

	// Always set stdout and stderr separately
	result.Stdout = string(streamResult.Stdout)
	result.Stderr = string(streamResult.Stderr)

	// Log execution results for debugging and monitoring
	logger.WithJobID(job.ID).Info("Job execution completed",
		zap.String("stdout", result.Stdout),
		zap.String("stderr", result.Stderr),
		zap.String("error", result.Error),
		zap.Bool("success", result.Success),
		zap.Duration("exec_time", result.ExecTime))

	return result
}
