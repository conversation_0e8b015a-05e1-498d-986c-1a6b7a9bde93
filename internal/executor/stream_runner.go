package executor

import (
	"bufio"
	"context"
	"errors"
	"fmt"
	"io"
	"os/exec"
	"runtime"
	"sync"
	"syscall"
	"time"

	"code-sandbox/internal/logger"

	"go.uber.org/zap"
)

// StreamResult contains the execution result with separate stdout/stderr
type StreamResult struct {
	Stdout   []byte
	Stderr   []byte
	ExitCode int
	Error    error
}

// StreamRunner provides simplified streaming output capture with proper resource management
type StreamRunner struct {
	cmd     *exec.Cmd
	ctx     context.Context
	timeout bool
}

// NewStreamRunner creates a new streaming runner with context-based timeout
func NewStreamRunner(cmd *exec.Cmd, ctx context.Context) *StreamRunner {
	return &StreamRunner{
		cmd: cmd,
		ctx: ctx,
	}
}

// Execute runs the command and captures output with proper resource management
func (r *StreamRunner) Execute() StreamResult {
	result := StreamResult{}

	// Create pipes
	stdoutPipe, err := r.cmd.StdoutPipe()
	if err != nil {
		result.Error = fmt.Errorf("创建stdout管道失败: %v", err)
		return result
	}

	stderrPipe, err := r.cmd.StderrPipe()
	if err != nil {
		r.safeClose(stdoutPipe)
		result.Error = fmt.Errorf("创建stderr管道失败: %v", err)
		return result
	}

	// Ensure pipes are always closed
	defer r.safeClose(stdoutPipe)
	defer r.safeClose(stderrPipe)

	// Start the process
	if err := r.cmd.Start(); err != nil {
		result.Error = fmt.Errorf("启动进程失败: %v", err)
		return result
	}

	// Track goroutines to ensure proper cleanup
	var wg sync.WaitGroup
	var mu sync.Mutex // Protect result updates

	// Channel for pipe results
	type pipeResult struct {
		data []byte
		err  error
	}

	stdoutCh := make(chan pipeResult, 1)
	stderrCh := make(chan pipeResult, 1)

	// Read stdout goroutine
	wg.Add(1)
	go func() {
		defer wg.Done()
		data, err := r.readPipe(stdoutPipe)
		stdoutCh <- pipeResult{data: data, err: err}
	}()

	// Read stderr goroutine
	wg.Add(1)
	go func() {
		defer wg.Done()
		data, err := r.readPipe(stderrPipe)
		stderrCh <- pipeResult{data: data, err: err}
	}()

	// Process monitoring goroutine - handles timeout cleanup
	done := make(chan struct{})
	go func() {
		defer close(done)

		// Wait for both streams to complete OR context cancellation
		var stdoutResult, stderrResult pipeResult
		stdoutDone, stderrDone := false, false

		for !stdoutDone || !stderrDone {
			select {
			case stdoutResult = <-stdoutCh:
				stdoutDone = true
			case stderrResult = <-stderrCh:
				stderrDone = true
			case <-r.ctx.Done():
				// Context cancelled or timed out - cleanup immediately
				r.timeout = true
				r.killProcessGroup()

				// Close pipes to unblock goroutines
				r.safeClose(stdoutPipe)
				r.safeClose(stderrPipe)

				// Wait for goroutines to finish with timeout
				if r.waitForGoRoutines(&wg, 2*time.Second) {
					logger.Info("Goroutines cleaned up successfully after timeout")
				} else {
					logger.Warn("Some goroutines may still be running after timeout")
				}

				mu.Lock()
				result.Error = fmt.Errorf("执行超时或被取消")
				mu.Unlock()
				return
			}
		}

		// Process completed normally - set results
		mu.Lock()
		result.Stdout = stdoutResult.data
		result.Stderr = stderrResult.data
		if stdoutResult.err != nil {
			result.Error = fmt.Errorf("读取stdout失败: %v", stdoutResult.err)
		} else if stderrResult.err != nil {
			result.Error = fmt.Errorf("读取stderr失败: %v", stderrResult.err)
		}
		mu.Unlock()
	}()

	// Wait for either completion or timeout
	<-done

	// Wait for process completion if not already killed
	if !r.timeout {
		if err := r.cmd.Wait(); err != nil {
			mu.Lock()
			if r.ctx.Err() != nil {
				r.timeout = true
				result.Error = fmt.Errorf("执行超时或被取消")
			} else {
				var exitErr *exec.ExitError
				if errors.As(err, &exitErr) {
					result.ExitCode = exitErr.ExitCode()
				} else {
					result.Error = fmt.Errorf("进程等待失败: %v", err)
				}
			}
			mu.Unlock()
		}
	}

	return result
}

// IsTimeout returns true if the execution timed out
func (r *StreamRunner) IsTimeout() bool {
	return r.timeout
}

// readPipe reads all data from a pipe using buffered scanner
func (r *StreamRunner) readPipe(pipe io.ReadCloser) ([]byte, error) {
	var data []byte
	scanner := bufio.NewScanner(pipe)

	// Set up scanner with context cancellation
	for scanner.Scan() {
		// Check if context was cancelled during read
		select {
		case <-r.ctx.Done():
			return data, r.ctx.Err()
		default:
		}

		line := scanner.Bytes()
		lineCopy := make([]byte, len(line)+1)
		copy(lineCopy, line)
		lineCopy[len(line)] = '\n'
		data = append(data, lineCopy...)
	}

	return data, scanner.Err()
}

// safeClose closes an io.Closer safely, logging any errors
func (r *StreamRunner) safeClose(closer io.Closer) {
	if closer != nil {
		if err := closer.Close(); err != nil {
			logger.Warn("Failed to close pipe", zap.Error(err))
		}
	}
}

// killProcessGroup kills the entire process group to ensure no zombie processes
func (r *StreamRunner) killProcessGroup() {
	if r.cmd.Process == nil {
		return
	}

	pid := r.cmd.Process.Pid

	// On Linux, kill the entire process group
	if runtime.GOOS == "linux" {
		// Kill process group (-pid sends signal to process group)
		if err := syscall.Kill(-pid, syscall.SIGKILL); err != nil {
			logger.Warn("Failed to kill process group, falling back to single process", zap.Int("pid", pid), zap.Error(err))
			// Fallback to killing just the main process
			if err := r.cmd.Process.Kill(); err != nil {
				logger.Error("Failed to kill process", zap.Int("pid", pid), zap.Error(err))
			}
		} else {
			logger.Info("Successfully killed process group", zap.Int("pid", pid))
		}
	} else {
		// On other platforms, just kill the main process
		if err := r.cmd.Process.Kill(); err != nil {
			logger.Error("Failed to kill process", zap.Int("pid", pid), zap.Error(err))
		} else {
			logger.Info("Successfully killed process", zap.Int("pid", pid))
		}
	}
}

// waitForGoRoutines waits for goroutines to finish with timeout
func (r *StreamRunner) waitForGoRoutines(wg *sync.WaitGroup, timeout time.Duration) bool {
	done := make(chan struct{})

	go func() {
		defer close(done)
		wg.Wait()
	}()

	select {
	case <-done:
		return true
	case <-time.After(timeout):
		return false
	}
}

// CombineOutput returns combined stdout and stderr for backward compatibility
func (result *StreamResult) CombineOutput() []byte {
	if len(result.Stderr) == 0 {
		return result.Stdout
	}
	if len(result.Stdout) == 0 {
		return result.Stderr
	}

	combined := make([]byte, 0, len(result.Stdout)+len(result.Stderr))
	combined = append(combined, result.Stdout...)
	combined = append(combined, result.Stderr...)
	return combined
}

// HasError returns true if there was a system error or non-zero exit code
func (result *StreamResult) HasError() bool {
	return result.Error != nil || result.ExitCode != 0
}
