package executor

import (
	"context"
	"fmt"
	"time"

	"code-sandbox/internal/config"
	"code-sandbox/internal/logger"

	"github.com/panjf2000/ants/v2"
	"go.uber.org/zap"
)

// Pool manages Python code execution using ants worker pool
type Pool struct {
	config   *config.Config
	antsPool *ants.PoolWithFunc
	executor *Executor
	ctx      context.Context
	cancel   context.CancelFunc
}

// NewPool creates a new execution pool with the given configuration using ants
func NewPool(cfg *config.Config) *Pool {
	ctx, cancel := context.WithCancel(context.Background())

	// Create executor instance
	executor := NewExecutor(cfg)

	pool := &Pool{
		config:   cfg,
		executor: executor,
		ctx:      ctx,
		cancel:   cancel,
	}

	// Create ants pool with task function
	antsPool, err := ants.NewPoolWithFunc(cfg.WorkerCount, pool.taskFunc, ants.WithOptions(ants.Options{
		ExpiryDuration:   time.Second * 10,
		PreAlloc:         true,
		MaxBlockingTasks: cfg.QueueSize,
		Nonblocking:      false,
		PanicHandler: func(p interface{}) {
			logger.Error("Worker panic recovered", zap.Any("panic", p))
		},
	}))

	if err != nil {
		logger.Fatal("Failed to create ants pool", zap.Error(err))
	}

	pool.antsPool = antsPool

	logger.Info("Started ants pool",
		zap.Int("workers", cfg.WorkerCount),
		zap.Int("max_blocking_tasks", cfg.QueueSize),
	)
	return pool
}

// taskFunc processes jobs using ants pool
func (p *Pool) taskFunc(payload interface{}) {
	job, ok := payload.(*Job)
	if !ok {
		logger.Error("Invalid job payload type")
		return
	}

	// Handle panics during job execution
	defer func() {
		if r := recover(); r != nil {
			logger.Error("Panic during job execution",
				zap.String("job_id", job.ID),
				zap.Any("panic", r),
			)
			job.ResultCh <- Result{
				ID:      job.ID,
				Success: false,
				Error:   fmt.Sprintf("Internal error: worker panic - %v", r),
			}
		}
	}()

	// Pass pool context to executor for proper cancellation chain
	result := p.executor.Execute(p.ctx, *job)
	job.ResultCh <- result
}

// SubmitJob submits code for asynchronous execution and returns a result channel
func (p *Pool) SubmitJob(job Job) <-chan Result {
	resultCh := make(chan Result, 1)
	job.ResultCh = resultCh

	// Submit job to ants pool
	err := p.antsPool.Invoke(&job)
	if err != nil {
		// Pool is full or other error
		go func() {
			resultCh <- Result{
				ID:      job.ID,
				Success: false,
				Error:   fmt.Sprintf("执行队列已满，请稍后重试: %v", err),
			}
		}()
	}

	return resultCh
}

// Shutdown gracefully stops the pool and all workers
func (p *Pool) Shutdown() {
	logger.Info("Shutting down execution pool")
	p.cancel()
	p.antsPool.Release()
	logger.Info("Execution pool stopped")
}

// Status returns the current pool status information
func (p *Pool) Status() map[string]interface{} {
	return map[string]interface{}{
		"workers":    p.config.WorkerCount,
		"queue_size": p.config.QueueSize,
		"timeout":    p.config.ExecTimeout.String(),
		"running":    p.antsPool.Running(),
		"free":       p.antsPool.Free(),
		"waiting":    p.antsPool.Waiting(),
	}
}
