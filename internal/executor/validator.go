package executor

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"os/exec"
	"strings"
	"time"

	"code-sandbox/internal/config"
	"code-sandbox/internal/logger"

	"go.uber.org/zap"
)

// SecurityRisk represents different levels of code risk
type SecurityRisk int

const (
	RiskSafe SecurityRisk = iota
	RiskLow
	RiskMedium
	RiskHigh
	RiskCritical
)

// SecurityViolation represents a detected security issue
type SecurityViolation struct {
	Type        string       `json:"type"`
	Description string       `json:"description"`
	Risk        SecurityRisk `json:"risk"`
	Line        int          `json:"line,omitempty"`
	Column      int          `json:"column,omitempty"`
	Pattern     string       `json:"pattern,omitempty"`
	Context     string       `json:"context,omitempty"`
}

// ValidationResult contains the outcome of security validation
type ValidationResult struct {
	Safe         bool                `json:"safe"`
	Risk         SecurityRisk        `json:"risk"`
	Violations   []SecurityViolation `json:"violations"`
	Duration     time.Duration       `json:"duration"`
	AnalysisType string              `json:"analysis_type"`
}

// PythonValidationResult represents the result from Python validator script
type PythonValidationResult struct {
	Safe         bool                      `json:"safe"`
	Risk         int                       `json:"risk"`
	Violations   []PythonSecurityViolation `json:"violations"`
	DurationMs   float64                   `json:"duration_ms"`
	AnalysisType string                    `json:"analysis_type"`
}

// PythonSecurityViolation represents a security violation from Python validator
type PythonSecurityViolation struct {
	Type        string  `json:"type"`
	Description string  `json:"description"`
	Risk        int     `json:"risk"`
	Line        *int    `json:"line"`
	Column      *int    `json:"column"`
	Pattern     *string `json:"pattern"`
	Context     *string `json:"context"`
}

// CodeValidator performs security analysis on Python code
type CodeValidator struct {
	config            *config.SecurityConfig
	validatorPath     string
	validationTimeout time.Duration
}

// NewCodeValidator creates a new security validator with configuration
func NewCodeValidator(cfg *config.SecurityConfig) *CodeValidator {
	validatorPath := "/app/scripts/python_validator.py"
	return &CodeValidator{
		config:            cfg,
		validatorPath:     validatorPath,
		validationTimeout: 10 * time.Second, // 10 second timeout for validation
	}
}

// ValidateCode performs comprehensive security analysis on Python code
func (cv *CodeValidator) ValidateCode(code string) ValidationResult {
	startTime := time.Now()

	// Check if security validation is enabled
	if !cv.config.Enable {
		logger.Info("Security validation disabled, skipping code validation")
		return ValidationResult{
			Safe:         true,
			Risk:         RiskSafe,
			Violations:   []SecurityViolation{},
			Duration:     time.Since(startTime),
			AnalysisType: "disabled",
		}
	}

	// Use AST-based Python validator
	result, err := cv.validateWithPython(code)
	if err != nil {
		logger.Error("Python validation failed", zap.Error(err))
		return ValidationResult{
			Safe: false,
			Risk: RiskCritical,
			Violations: []SecurityViolation{{
				Type:        "validation_error",
				Description: fmt.Sprintf("Code validation failed: %v", err),
				Risk:        RiskCritical,
			}},
			Duration:     time.Since(startTime),
			AnalysisType: "error",
		}
	}

	// Convert Python result to Go result
	goResult := cv.convertPythonResult(result, startTime)

	logger.Info("Code validation completed",
		zap.Bool("safe", goResult.Safe),
		zap.Int("risk_level", int(goResult.Risk)),
		zap.Int("violations_count", len(goResult.Violations)),
		zap.Duration("duration", goResult.Duration),
		zap.String("analysis_type", goResult.AnalysisType))

	return goResult
}

// validateWithPython calls the Python AST validator script
func (cv *CodeValidator) validateWithPython(code string) (*PythonValidationResult, error) {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), cv.validationTimeout)
	defer cancel()

	// Prepare configuration for Python script
	configJSON, err := json.Marshal(cv.config)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal config: %w", err)
	}

	// Debug: Log the configuration being sent to Python validator
	logger.Info("Sending configuration to Python validator", zap.String("config", string(configJSON)))

	// Encode config in base64 to safely pass via environment
	configBase64 := base64.StdEncoding.EncodeToString(configJSON)

	// Create command with proper argument handling (no shell injection)
	cmd := exec.CommandContext(ctx, "python3", cv.validatorPath)

	// Set environment variables
	cmd.Env = append(os.Environ(), fmt.Sprintf("VALIDATOR_CONFIG=%s", configBase64))

	// Prepare input/output
	var stdout, stderr bytes.Buffer
	cmd.Stdin = strings.NewReader(code)
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	// Execute the command
	err = cmd.Run()

	// Check for context timeout
	if errors.Is(ctx.Err(), context.DeadlineExceeded) {
		return nil, fmt.Errorf("validation timeout after %v", cv.validationTimeout)
	}

	// Check for execution errors
	if err != nil {
		stderrStr := stderr.String()
		return nil, fmt.Errorf("python validator failed: %w, stderr: %s", err, stderrStr)
	}

	// Parse JSON result
	var result PythonValidationResult
	if err := json.Unmarshal(stdout.Bytes(), &result); err != nil {
		return nil, fmt.Errorf("failed to parse validation result: %w, output: %s", err, stdout.String())
	}

	return &result, nil
}

// convertPythonResult converts Python validation result to Go result
func (cv *CodeValidator) convertPythonResult(pythonResult *PythonValidationResult, startTime time.Time) ValidationResult {
	// Convert violations
	goViolations := make([]SecurityViolation, len(pythonResult.Violations))
	for i, pv := range pythonResult.Violations {
		goViolations[i] = SecurityViolation{
			Type:        pv.Type,
			Description: pv.Description,
			Risk:        SecurityRisk(pv.Risk),
		}

		// Handle optional fields
		if pv.Line != nil {
			goViolations[i].Line = *pv.Line
		}
		if pv.Column != nil {
			goViolations[i].Column = *pv.Column
		}
		if pv.Pattern != nil {
			goViolations[i].Pattern = *pv.Pattern
		}
		if pv.Context != nil {
			goViolations[i].Context = *pv.Context
		}
	}

	return ValidationResult{
		Safe:         pythonResult.Safe,
		Risk:         SecurityRisk(pythonResult.Risk),
		Violations:   goViolations,
		Duration:     time.Since(startTime),
		AnalysisType: pythonResult.AnalysisType,
	}
}

// ShouldBlock determines if code execution should be blocked based on validation
func (result *ValidationResult) ShouldBlock() bool {
	return result.Risk >= RiskCritical
}

// GetRiskDescription returns human-readable risk description
func (risk SecurityRisk) GetRiskDescription() string {
	switch risk {
	case RiskSafe:
		return "Safe"
	case RiskLow:
		return "Low Risk"
	case RiskMedium:
		return "Medium Risk"
	case RiskHigh:
		return "High Risk"
	case RiskCritical:
		return "Critical Risk"
	default:
		return "Unknown Risk"
	}
}

// UpdateConfig updates the validator configuration at runtime
func (cv *CodeValidator) UpdateConfig(newConfig *config.SecurityConfig) {
	cv.config = newConfig
	logger.Info("Validator configuration updated",
		zap.Bool("security_enabled", newConfig.Enable),
		zap.Int("blocked_imports", len(newConfig.BlockedImports)))
}

// GetConfig returns the current validator configuration
func (cv *CodeValidator) GetConfig() *config.SecurityConfig {
	return cv.config
}
