package middleware

import (
	"code-sandbox/internal/logger"
	"net/http"
)

// AuthMiddleware creates a Bearer Token authentication middleware
func AuthMiddleware(token string) func(http.HandlerFunc) http.HandlerFunc {
	return func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			authHeader := r.Header.Get("Authorization")
			expectedToken := "Bearer " + token

			if authHeader == "" {
				http.Error(w, "Authorization header is required", http.StatusUnauthorized)
				logger.Error("Authentication failed: missing Authorization header")
				return
			}

			if authHeader != expectedToken {
				http.Error(w, "Invalid token", http.StatusUnauthorized)
				logger.Error("Authentication failed: invalid token")
				return
			}

			logger.Info("Request authenticated successfully")
			next(w, r)
		}
	}
}
