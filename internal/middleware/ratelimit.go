package middleware

import (
	"context"
	"net/http"
	"time"

	"code-sandbox/internal/logger"

	"go.uber.org/ratelimit"
)

// GlobalRateLimiter 全局限流器
type GlobalRateLimiter struct {
	limiter ratelimit.Limiter
}

// NewGlobalRateLimiter 创建全局限流器
func NewGlobalRateLimiter(rps int) *GlobalRateLimiter {
	return &GlobalRateLimiter{
		limiter: ratelimit.New(rps), // 每秒允许的请求数
	}
}

// RateLimitMiddleware 限流中间件
func RateLimitMiddleware(rl *GlobalRateLimiter) func(http.HandlerFunc) http.HandlerFunc {
	return func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			// 使用context设置最大等待时间，避免无限阻塞
			ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
			defer cancel()

			done := make(chan struct{})
			go func() {
				rl.limiter.Take() // 阻塞直到获得令牌
				close(done)
			}()

			select {
			case <-done:
				// 获得令牌，继续处理
				logger.Debug("Rate limit: Request allowed")
				next(w, r)
			case <-ctx.Done():
				// 等待超时
				logger.Warn("Rate limit: Request timeout after 5 seconds")
				http.Error(w, "Service temporarily unavailable", http.StatusServiceUnavailable)
				return
			}
		}
	}
}
