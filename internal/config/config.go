package config

import (
	"errors"
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"gopkg.in/yaml.v3"
)

// LogConfig represents logging configuration
type LogConfig struct {
	Level      string `yaml:"level"`       // debug, info, warn, error
	FilePath   string `yaml:"file_path"`   // logs/app.log
	MaxSize    int    `yaml:"max_size"`    // MB per file
	MaxAge     int    `yaml:"max_age"`     // days to keep
	MaxBackups int    `yaml:"max_backups"` // number of backups
	Compress   bool   `yaml:"compress"`    // compress old logs
	Console    bool   `yaml:"console"`     // output to console
	FileOutput bool   `yaml:"file_output"` // output to file
	Async      bool   `yaml:"async"`       // async writing
	BufferSize int    `yaml:"buffer_size"` // async buffer size
}

// SecurityConfig represents security validation configuration
type SecurityConfig struct {
	Enable          bool                 `yaml:"enable" json:"enable"`                     // Global security validation switch
	BlockedImports  []string             `yaml:"blocked_imports" json:"blocked_imports"`   // Blocked imports (execution will be blocked)
	NetworkRequests NetworkRequestConfig `yaml:"network_requests" json:"network_requests"` // Network request control
	ResourceLimits  ResourceLimitConfig  `yaml:"resource_limits" json:"resource_limits"`   // Resource limits
	Validator       ValidatorConfig      `yaml:"validator" json:"validator"`               // Validator behavior
}

// NetworkRequestConfig controls network request access
type NetworkRequestConfig struct {
	Allow               bool     `yaml:"allow" json:"allow"`                                 // Whether to allow network requests
	BlockedWhenDisabled []string `yaml:"blocked_when_disabled" json:"blocked_when_disabled"` // Modules to block when allow=false
	RiskLevel           string   `yaml:"risk_level" json:"risk_level"`                       // Risk level: "critical" or "high"
}

// ResourceLimitConfig defines resource usage limits
type ResourceLimitConfig struct {
	MaxLoopIterations       int `yaml:"max_loop_iterations" json:"max_loop_iterations"`             // Maximum loop iterations
	MaxMemoryAllocation     int `yaml:"max_memory_allocation" json:"max_memory_allocation"`         // Maximum memory allocation elements
	MaxStringRepetition     int `yaml:"max_string_repetition" json:"max_string_repetition"`         // Maximum string multiplication factor
	MaxExponentiation       int `yaml:"max_exponentiation" json:"max_exponentiation"`               // Maximum exponent value
	MaxLoopNesting          int `yaml:"max_loop_nesting" json:"max_loop_nesting"`                   // Maximum loop nesting depth
	MaxComprehensionNesting int `yaml:"max_comprehension_nesting" json:"max_comprehension_nesting"` // Maximum comprehension nesting
}

// ValidatorConfig defines validator behavior
type ValidatorConfig struct {
	EarlyTermination  bool `yaml:"early_termination" json:"early_termination"`   // Stop on first critical violation
	MaxViolations     int  `yaml:"max_violations" json:"max_violations"`         // Maximum violations to record
	ValidationTimeout int  `yaml:"validation_timeout" json:"validation_timeout"` // Validation timeout in seconds
}

// Config represents server configuration
type Config struct {
	ServerPort  string `yaml:"server_port"`
	WorkerCount int    `yaml:"worker_count"`
	QueueSize   int    `yaml:"queue_size"`

	AuthToken string `yaml:"auth_token"`

	ExecTimeout  time.Duration `yaml:"exec_timeout"`
	MaxCodeSize  int           `yaml:"max_code_size"`  // Maximum code size in bytes
	RateLimitRPS int           `yaml:"rate_limit_rps"` // Rate limit requests per second

	// Resource limits for ulimit wrapper
	MemoryLimitMB   int `yaml:"memory_limit_mb"`    // Memory limit in MB for Python processes
	FileSizeLimitKB int `yaml:"file_size_limit_kb"` // Maximum file size in KB for output
	MaxProcesses    int `yaml:"max_processes"`      // Maximum number of processes
	MaxOpenFiles    int `yaml:"max_open_files"`     // Maximum number of open files
	MaxStackSizeKB  int `yaml:"max_stack_size_kb"`  // Maximum stack size in KB

	// Logging configuration
	LogConfig LogConfig `yaml:"log"`

	// Security validation configuration
	SecurityConfig SecurityConfig `yaml:"security"`
}

// LoadConfig loads configuration from YAML file with environment variable overrides
// Config file is required - if not found or invalid, returns error
func LoadConfig(configPath string) (*Config, error) {
	// Config file is mandatory
	if configPath == "" {
		return nil, errors.New("config path is required")
	}

	// Read config file (fail if not exists)
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file '%s': %w", configPath, err)
	}

	// Parse YAML (fail if invalid)
	config := &Config{}
	if err := yaml.Unmarshal(data, config); err != nil {
		return nil, fmt.Errorf("failed to parse config file '%s': %w", configPath, err)
	}

	// Apply environment variable overrides
	config = applyEnvOverrides(config)

	log.Printf("Configuration loaded from: %s", configPath)
	return config, nil
}

// applyEnvOverrides applies environment variable overrides to existing config
func applyEnvOverrides(config *Config) *Config {
	if val := os.Getenv("AUTH_TOKEN"); val != "" {
		config.AuthToken = val
	}

	if val := os.Getenv("WORKER_COUNT"); val != "" {
		if count, err := strconv.Atoi(val); err == nil && count > 0 {
			config.WorkerCount = count
		}
	}

	if val := os.Getenv("QUEUE_SIZE"); val != "" {
		if size, err := strconv.Atoi(val); err == nil && size > 0 {
			config.QueueSize = size
		}
	}

	if val := os.Getenv("EXEC_TIMEOUT"); val != "" {
		if timeout, err := time.ParseDuration(val); err == nil {
			config.ExecTimeout = timeout
		}
	}

	if val := os.Getenv("SERVER_PORT"); val != "" {
		config.ServerPort = val
	}

	if val := os.Getenv("MAX_CODE_SIZE"); val != "" {
		if size, err := strconv.Atoi(val); err == nil && size > 0 {
			config.MaxCodeSize = size
		}
	}

	if val := os.Getenv("RATE_LIMIT_RPS"); val != "" {
		if limit, err := strconv.Atoi(val); err == nil && limit > 0 {
			config.RateLimitRPS = limit
		}
	}

	// Resource limits configuration
	if val := os.Getenv("MEMORY_LIMIT_MB"); val != "" {
		if limit, err := strconv.Atoi(val); err == nil && limit > 0 {
			config.MemoryLimitMB = limit
		}
	}

	if val := os.Getenv("FILE_SIZE_LIMIT_KB"); val != "" {
		if limit, err := strconv.Atoi(val); err == nil && limit > 0 {
			config.FileSizeLimitKB = limit
		}
	}

	if val := os.Getenv("MAX_PROCESSES"); val != "" {
		if limit, err := strconv.Atoi(val); err == nil && limit > 0 {
			config.MaxProcesses = limit
		}
	}

	if val := os.Getenv("MAX_OPEN_FILES"); val != "" {
		if limit, err := strconv.Atoi(val); err == nil && limit > 0 {
			config.MaxOpenFiles = limit
		}
	}

	if val := os.Getenv("MAX_STACK_SIZE_KB"); val != "" {
		if limit, err := strconv.Atoi(val); err == nil && limit > 0 {
			config.MaxStackSizeKB = limit
		}
	}

	// Logging configuration
	if val := os.Getenv("LOG_LEVEL"); val != "" {
		config.LogConfig.Level = val
	}

	if val := os.Getenv("LOG_FILE_PATH"); val != "" {
		config.LogConfig.FilePath = val
	}

	if val := os.Getenv("LOG_MAX_SIZE"); val != "" {
		if size, err := strconv.Atoi(val); err == nil && size > 0 {
			config.LogConfig.MaxSize = size
		}
	}

	if val := os.Getenv("LOG_MAX_AGE"); val != "" {
		if age, err := strconv.Atoi(val); err == nil && age > 0 {
			config.LogConfig.MaxAge = age
		}
	}

	if val := os.Getenv("LOG_MAX_BACKUPS"); val != "" {
		if backups, err := strconv.Atoi(val); err == nil && backups >= 0 {
			config.LogConfig.MaxBackups = backups
		}
	}

	if val := os.Getenv("LOG_COMPRESS"); val != "" {
		if compress, err := strconv.ParseBool(val); err == nil {
			config.LogConfig.Compress = compress
		}
	}

	if val := os.Getenv("LOG_CONSOLE"); val != "" {
		if console, err := strconv.ParseBool(val); err == nil {
			config.LogConfig.Console = console
		}
	}

	if val := os.Getenv("LOG_FILE_OUTPUT"); val != "" {
		if fileOutput, err := strconv.ParseBool(val); err == nil {
			config.LogConfig.FileOutput = fileOutput
		}
	}

	if val := os.Getenv("LOG_ASYNC"); val != "" {
		if async, err := strconv.ParseBool(val); err == nil {
			config.LogConfig.Async = async
		}
	}

	if val := os.Getenv("LOG_BUFFER_SIZE"); val != "" {
		if bufferSize, err := strconv.Atoi(val); err == nil && bufferSize > 0 {
			config.LogConfig.BufferSize = bufferSize
		}
	}

	// Keep using standard log for config loading to avoid circular dependency
	log.Printf("Final Config: Workers=%d, Queue=%d, Timeout=%s, Port=%s, Memory=%dMB, MaxCode=%d, RateLimit=%d/s | ResourceLimits: FileSize=%dKB, MaxProcs=%d, MaxFiles=%d, Stack=%dKB",
		config.WorkerCount, config.QueueSize, config.ExecTimeout, config.ServerPort, config.MemoryLimitMB,
		config.MaxCodeSize, config.RateLimitRPS, config.FileSizeLimitKB, config.MaxProcesses, config.MaxOpenFiles,
		config.MaxStackSizeKB)

	return config
}

// maskToken masks token for secure logging
func maskToken(token string) string {
	if len(token) <= 8 {
		return strings.Repeat("*", len(token))
	}
	return token[:4] + strings.Repeat("*", len(token)-8) + token[len(token)-4:]
}
