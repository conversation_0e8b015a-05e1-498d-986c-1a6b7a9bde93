package logger

import (
	"fmt"
	"os"
	"sync"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var (
	globalLogger *zap.Logger
	once         sync.Once
)

// customTimeEncoder formats time as "2006-01-02 15:04:05,000" with comma for milliseconds
func customTimeEncoder(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
	millis := t.UnixNano() / 1e6 % 1000
	enc.AppendString(t.Format("2006-01-02 15:04:05") + fmt.Sprintf(",%.3d", millis))
}

// LogConfig represents logging configuration (alias to avoid circular imports)
type LogConfig struct {
	Level      string // debug, info, warn, error
	FilePath   string // logs/app.log
	MaxSize    int    // MB per file
	MaxAge     int    // days to keep
	MaxBackups int    // number of backups
	Compress   bool   // compress old logs
	Console    bool   // output to console
	FileOutput bool   // output to file
	Async      bool   // async writing
	BufferSize int    // async buffer size
}

// Initialize sets up the global logger with the given configuration
func Initialize(config *LogConfig) error {
	var err error
	once.Do(func() {
		globalLogger, err = newLogger(config)
	})
	return err
}

// newLogger creates a new zap logger with the specified configuration
func newLogger(config *LogConfig) (*zap.Logger, error) {
	// Parse log level
	level, err := zapcore.ParseLevel(config.Level)
	if err != nil {
		level = zapcore.InfoLevel
	}

	// Create encoder config for console format
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:          "timestamp",
		LevelKey:         "level",
		NameKey:          "logger",
		CallerKey:        "caller", // Enable caller info to show file:line
		MessageKey:       "msg",
		StacktraceKey:    "",
		LineEnding:       zapcore.DefaultLineEnding,
		EncodeLevel:      zapcore.CapitalLevelEncoder, // INFO, WARN, ERROR
		EncodeTime:       customTimeEncoder,           // Custom format with comma
		EncodeDuration:   zapcore.StringDurationEncoder,
		EncodeCaller:     zapcore.ShortCallerEncoder,
		ConsoleSeparator: " - ", // Separator between fields
	}

	// Create console encoder
	encoder := zapcore.NewConsoleEncoder(encoderConfig)

	// Create writer
	writer := NewMultiWriter(config)

	// Create syncer
	var syncer zapcore.WriteSyncer
	if config.Async {
		syncer = &zapcore.BufferedWriteSyncer{
			WS:   zapcore.AddSync(writer),
			Size: config.BufferSize,
		}
	} else {
		syncer = zapcore.AddSync(writer)
	}

	// Create core
	core := zapcore.NewCore(encoder, syncer, level)

	// Create logger with caller skip to show actual calling code location
	logger := zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1), zap.AddStacktrace(zapcore.ErrorLevel))

	return logger, nil
}

// GetLogger returns the global logger instance
func GetLogger() *zap.Logger {
	if globalLogger == nil {
		// Fallback logger if not initialized - use console format for consistency
		config := zapcore.EncoderConfig{
			TimeKey:          "timestamp",
			LevelKey:         "level",
			NameKey:          "logger",
			CallerKey:        "caller", // Enable caller info
			MessageKey:       "msg",
			StacktraceKey:    "stacktrace",
			LineEnding:       zapcore.DefaultLineEnding,
			EncodeLevel:      zapcore.CapitalLevelEncoder,
			EncodeTime:       customTimeEncoder,
			EncodeDuration:   zapcore.StringDurationEncoder,
			EncodeCaller:     zapcore.ShortCallerEncoder,
			ConsoleSeparator: " - ",
		}
		core := zapcore.NewCore(
			zapcore.NewConsoleEncoder(config),
			zapcore.AddSync(os.Stdout),
			zapcore.InfoLevel,
		)
		globalLogger = zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1))
	}
	return globalLogger
}

// Sync flushes any buffered log entries
func Sync() error {
	if globalLogger != nil {
		return globalLogger.Sync()
	}
	return nil
}

// Helper functions for common logging patterns
func Info(msg string, fields ...zap.Field) {
	GetLogger().Info(msg, fields...)
}

func Debug(msg string, fields ...zap.Field) {
	GetLogger().Debug(msg, fields...)
}

func Warn(msg string, fields ...zap.Field) {
	GetLogger().Warn(msg, fields...)
}

func Error(msg string, fields ...zap.Field) {
	GetLogger().Error(msg, fields...)
}

func Fatal(msg string, fields ...zap.Field) {
	GetLogger().Fatal(msg, fields...)
}

// WithFields creates a logger with predefined fields
func WithFields(fields ...zap.Field) *zap.Logger {
	return GetLogger().With(fields...)
}

// WithJobID creates a logger with job ID field
func WithJobID(jobID string) *zap.Logger {
	return GetLogger().With(zap.String("job_id", jobID))
}

// WithRequestID creates a logger with request ID field
func WithRequestID(requestID string) *zap.Logger {
	return GetLogger().With(zap.String("request_id", requestID))
}

// WithComponent creates a logger with component field
func WithComponent(component string) *zap.Logger {
	return GetLogger().With(zap.String("component", component))
}

// LogExecutionTime logs the execution time of an operation
func LogExecutionTime(logger *zap.Logger, operation string, start time.Time) {
	duration := time.Since(start)
	logger.Info("Operation completed",
		zap.String("operation", operation),
		zap.Duration("duration", duration),
	)
}
