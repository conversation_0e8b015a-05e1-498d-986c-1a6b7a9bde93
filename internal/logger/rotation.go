package logger

import (
	"io"
	"os"

	"gopkg.in/natefinch/lumberjack.v2"
)

// NewRotationWriter creates a rotation writer using lumberjack
func NewRotationWriter(config *LogConfig) io.Writer {
	return &lumberjack.Logger{
		Filename:   config.FilePath,
		MaxSize:    config.MaxSize,
		MaxBackups: config.MaxBackups,
		MaxAge:     config.MaxAge,
		Compress:   config.Compress,
	}
}

// NewMultiWriter creates a multi-writer that can write to both console and file
func NewMultiWriter(config *LogConfig) io.Writer {
	writers := make([]io.Writer, 0, 2)

	if config.Console {
		writers = append(writers, os.Stdout)
	}

	if config.FileOutput {
		fileWriter := NewRotationWriter(config)
		writers = append(writers, fileWriter)
	}

	if len(writers) == 0 {
		// Fallback to stdout if nothing is configured
		return os.Stdout
	}

	if len(writers) == 1 {
		return writers[0]
	}

	return io.MultiWriter(writers...)
}
