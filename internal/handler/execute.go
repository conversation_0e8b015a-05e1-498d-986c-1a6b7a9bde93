package handler

import (
	"encoding/base64"
	"encoding/json"
	"net/http"
	"regexp"
	"strings"
	"time"

	"code-sandbox/internal/config"
	"code-sandbox/internal/executor"
	"code-sandbox/internal/logger"

	"github.com/google/uuid"
	"go.uber.org/zap"
)

// ExecuteHandler handles code execution requests
type ExecuteHandler struct {
	pool *executor.Pool
	cfg  *config.Config
}

// NewExecuteHandler creates a new execute handler
func NewExecuteHandler(pool *executor.Pool, cfg *config.Config) *ExecuteHandler {
	return &ExecuteHandler{pool: pool, cfg: cfg}
}

// Handle processes code execution requests
func (h *ExecuteHandler) Handle(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Parse request
	var req executor.ExecuteRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Validate code_base64 is not empty
	if req.CodeBase64 == "" {
		http.Error(w, "code_base64 cannot be empty", http.StatusBadRequest)
		return
	}

	// Base64 decode
	codeBytes, err := base64.StdEncoding.DecodeString(req.CodeBase64)
	if err != nil {
		http.Error(w, "Invalid Base64 encoding", http.StatusBadRequest)
		logger.Error("Base64 decode error", zap.Error(err))
		return
	}

	code := string(codeBytes)

	// Validate decoded code is not empty
	if strings.TrimSpace(code) == "" {
		http.Error(w, "Decoded code cannot be empty", http.StatusBadRequest)
		return
	}

	// Validate decoded code size limit
	if len(code) > h.cfg.MaxCodeSize {
		http.Error(w, "Decoded code size exceeds limit", http.StatusBadRequest)
		return
	}

	// Log request
	logger.Info("Received code execution request",
		zap.Int("encoded_bytes", len(req.CodeBase64)),
		zap.Int("decoded_bytes", len(code)))

	// Create job
	job := executor.Job{
		ID:   uuid.New().String(),
		Code: code,
	}

	// Execute code asynchronously
	resultCh := h.pool.SubmitJob(job)

	// Wait for execution result
	select {
	case result := <-resultCh:
		// Build response with warning generation
		response := executor.ExecuteResponse{
			ID:         result.ID,
			Stdout:     result.Stdout,
			Stderr:     result.Stderr,
			Error:      result.Error,
			Success:    result.Success,
			ExecTimeMs: result.ExecTime.Milliseconds(),
			Warning:    h.generateWarning(code, result.ExecTime),
		}

		// Set response headers
		w.Header().Set("Content-Type", "application/json")

		// Always return HTTP 200 for successful HTTP requests
		w.WriteHeader(http.StatusOK)

		// Return JSON response
		if err := json.NewEncoder(w).Encode(response); err != nil {
			logger.Error("Failed to encode JSON response", zap.Error(err))
		}

		logger.Info("Code execution completed",
			zap.String("job_id", result.ID),
			zap.Bool("success", result.Success),
			zap.Int64("exec_time_ms", result.ExecTime.Milliseconds()))

	case <-time.After(h.cfg.ExecTimeout + 5*time.Second): // Slightly longer than execution timeout
		http.Error(w, "Request timeout", http.StatusRequestTimeout)
		logger.Warn("Request timeout waiting for execution result")
	}
}

// generateWarning creates performance warnings based on execution time and code analysis
func (h *ExecuteHandler) generateWarning(code string, execTime time.Duration) string {
	var warnings []string

	// Define heavy packages that cause import delays
	heavyPackages := map[string]string{
		"pandas":     "pandas",
		"numpy":      "numpy",
		"matplotlib": "matplotlib/pyplot",
		"scipy":      "scipy",
		"seaborn":    "seaborn",
		"sklearn":    "scikit-learn",
		"tensorflow": "tensorflow",
		"torch":      "pytorch",
	}

	// Check for heavy package imports
	var detectedPackages []string
	for pattern, name := range heavyPackages {
		// Match import patterns: import pandas, from pandas import, import pandas as pd
		importRegex := regexp.MustCompile(`(?m)^(?:\s*(?:import|from)\s+` + pattern + `(?:\s|$|\.)|\s*import\s+.*` + pattern + `.*)`)
		if importRegex.MatchString(code) {
			detectedPackages = append(detectedPackages, name)
		}
	}

	// Performance warnings based on execution time
	execSeconds := execTime.Seconds()

	if execSeconds > 60 {
		if len(detectedPackages) > 0 {
			warnings = append(warnings,
				"⚠️ High execution time ("+formatDuration(execTime)+") detected with heavy packages: "+
					strings.Join(detectedPackages, ", ")+
					". Consider: 1) Import only needed modules (e.g. 'from pandas import DataFrame'), "+
					"2) Use lighter alternatives when possible, 3) Cache results for repeated operations.")
		} else {
			warnings = append(warnings,
				"⚠️ High execution time ("+formatDuration(execTime)+") detected. "+
					"Consider optimizing algorithms or reducing computational complexity.")
		}
	} else if execSeconds > 30 && len(detectedPackages) > 0 {
		warnings = append(warnings,
			"💡 Heavy packages detected: "+strings.Join(detectedPackages, ", ")+
				". First import may take longer ("+formatDuration(execTime)+"). "+
				"Subsequent runs will be faster within the same session.")
	}

	// Join all warnings
	if len(warnings) > 0 {
		return strings.Join(warnings, " ")
	}

	return ""
}

// formatDuration formats duration in a human-readable way
func formatDuration(d time.Duration) string {
	if d < time.Second {
		return d.Round(time.Millisecond).String()
	} else if d < time.Minute {
		return d.Round(time.Second).String()
	} else {
		return d.Round(time.Second).String()
	}
}
