package handler

import (
	"encoding/json"
	"net/http"
	"time"

	"code-sandbox/internal/executor"
	"code-sandbox/internal/logger"

	"go.uber.org/zap"
)

// HealthHandler handles health check and status requests
type HealthHandler struct {
	pool *executor.Pool
}

// NewHealthHandler creates a new health handler
func NewHealthHandler(pool *executor.Pool) *HealthHandler {
	return &HealthHandler{pool: pool}
}

// HandleStatus handles status query requests
func (h *HealthHandler) HandleStatus(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	status := h.pool.Status()
	status["timestamp"] = time.Now().Format(time.RFC3339)

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(status); err != nil {
		http.Error(w, "Failed to encode status", http.StatusInternalServerError)
	}
}

// HandleHealth handles health checks
func (h *HealthHandler) HandleHealth(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	w.WriteHeader(http.StatusOK)
	if _, err := w.Write([]byte("OK")); err != nil {
		// Log error but don't change response since header already written
		logger.Error("Failed to write health response", zap.Error(err))
	}
}
