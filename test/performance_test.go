package test

import (
	"fmt"
	"sync"
	"testing"
	"time"

	"code-sandbox/test/testdata"
)

func TestPerformance(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("ComplexCalculation", func(t *testing.T) {
		if testing.Short() {
			t.<PERSON><PERSON>("Skipping complex calculation test in short mode")
		}
		t.<PERSON><PERSON>()

		response, responseTime, err := client.ExecuteCode(testdata.ComplexCalculation)
		AssertSuccess(t, response, err, responseTime)

		// Complex calculation should complete within reasonable time
		maxTime := 60 * time.Second
		AssertResponseTime(t, responseTime, maxTime)

		// Verify output contains expected results
		AssertOutputContains(t, response, "计算用时:")
		AssertOutputContains(t, response, "特征值数量:")
		AssertOutputContains(t, response, "✅ 复杂计算完成")

		t.Logf("Complex calculation completed in %v (execution: %dms)",
			responseTime, response.ExecTimeMs)
	})

	t.Run("MemoryIntensiveOperation", func(t *testing.T) {
		if testing.Short() {
			t.Skip("Skipping memory intensive test in short mode")
		}
		t.<PERSON>()

		code := `
import numpy as np
import gc

print("开始内存密集型操作...")

# 创建大数组但在内存限制内
arrays = []
for i in range(10):
    arr = np.random.random((1000, 1000))  # ~8MB per array
    result = np.sum(arr)
    arrays.append(result)  # Only keep the sum, not the full array
    
    if i % 3 == 0:
        gc.collect()  # Periodic garbage collection
    
    print(f"数组 {i+1}: 和 = {result:.2f}")

print(f"总共处理了 {len(arrays)} 个大数组")
print(f"所有数组和的平均值: {np.mean(arrays):.2f}")
print("✅ 内存密集型操作完成")
`
		response, responseTime, err := client.ExecuteCode(code)
		AssertSuccess(t, response, err, responseTime)

		maxTime := 30 * time.Second
		AssertResponseTime(t, responseTime, maxTime)

		AssertOutputContains(t, response, "内存密集型操作完成")
		AssertOutputContains(t, response, "总共处理了 10 个大数组")

		t.Logf("Memory intensive operation completed in %v", responseTime)
	})

	t.Run("CPUIntensiveOperation", func(t *testing.T) {
		if testing.Short() {
			t.Skip("Skipping CPU intensive test in short mode")
		}
		t.Parallel()

		code := `
import time
import math

print("开始CPU密集型操作...")
start_time = time.time()

# 计算大量质数
def is_prime(n):
    if n < 2:
        return False
    for i in range(2, int(math.sqrt(n)) + 1):
        if n % i == 0:
            return False
    return True

primes = []
for i in range(2, 10000):
    if is_prime(i):
        primes.append(i)

# 计算一些数学运算
results = []
for p in primes[:100]:  # 使用前100个质数
    result = p ** 2 + p ** 3
    results.append(result)

cpu_time = time.time() - start_time

print(f"找到质数数量: {len(primes)}")
print(f"前10个质数: {primes[:10]}")
print(f"CPU计算用时: {cpu_time:.3f}秒")
print(f"处理的数学运算: {len(results)}")
print("✅ CPU密集型操作完成")
`
		response, responseTime, err := client.ExecuteCode(code)
		AssertSuccess(t, response, err, responseTime)

		maxTime := 45 * time.Second
		AssertResponseTime(t, responseTime, maxTime)

		AssertOutputContains(t, response, "CPU密集型操作完成")
		AssertOutputContains(t, response, "找到质数数量:")

		t.Logf("CPU intensive operation completed in %v", responseTime)
	})
}

func TestConcurrency(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("ConcurrentRequests", func(t *testing.T) {
		if testing.Short() {
			t.Skip("Skipping concurrent test in short mode")
		}
		t.Parallel()

		numRequests := 50
		var wg sync.WaitGroup
		results := make([]TestResult, numRequests)

		start := time.Now()

		for i := 0; i < numRequests; i++ {
			wg.Add(1)
			go func(index int) {
				defer wg.Done()

				response, responseTime, err := client.ExecuteCode(testdata.ConcurrentTestScript)

				results[index] = TestResult{
					Name:         fmt.Sprintf("Concurrent-%d", index+1),
					Success:      err == nil && response != nil && response.Success,
					ResponseTime: responseTime,
				}

				if response != nil {
					results[index].ExecTime = time.Duration(response.ExecTimeMs) * time.Millisecond
				}

				if err != nil {
					results[index].Error = err.Error()
				} else if response != nil && !response.Success {
					results[index].Error = response.Error
				}
			}(i)
		}

		wg.Wait()
		totalTime := time.Since(start)

		// Analyze results
		successCount := 0
		var totalResponseTime time.Duration
		var totalExecTime time.Duration
		var maxResponseTime time.Duration
		minResponseTime := time.Hour // Initialize to large value

		for _, result := range results {
			if result.Success {
				successCount++
				totalResponseTime += result.ResponseTime
				totalExecTime += result.ExecTime

				if result.ResponseTime > maxResponseTime {
					maxResponseTime = result.ResponseTime
				}
				if result.ResponseTime < minResponseTime {
					minResponseTime = result.ResponseTime
				}
			}
		}

		// Calculate statistics
		successRate := float64(successCount) / float64(numRequests) * 100
		avgResponseTime := totalResponseTime / time.Duration(successCount)
		avgExecTime := totalExecTime / time.Duration(successCount)

		// Report results
		t.Logf("Concurrent test results:")
		t.Logf("  Total requests: %d", numRequests)
		t.Logf("  Successful: %d (%.1f%%)", successCount, successRate)
		t.Logf("  Total time: %v", totalTime)
		t.Logf("  Avg response time: %v", avgResponseTime)
		t.Logf("  Avg execution time: %v", avgExecTime)
		t.Logf("  Min response time: %v", minResponseTime)
		t.Logf("  Max response time: %v", maxResponseTime)

		// Assertions
		minSuccessRate := 80.0 // At least 80% should succeed
		if successRate < minSuccessRate {
			t.Errorf("Success rate %.1f%% below minimum %.1f%%", successRate, minSuccessRate)
		}

		// Average response time should be reasonable
		maxAvgResponseTime := 10 * time.Second
		if avgResponseTime > maxAvgResponseTime {
			t.Errorf("Average response time %v exceeds maximum %v", avgResponseTime, maxAvgResponseTime)
		}

		// Log failed requests
		failedCount := numRequests - successCount
		if failedCount > 0 {
			t.Logf("Failed requests (%d):", failedCount)
			for i, result := range results {
				if !result.Success {
					t.Logf("  Request %d: %s", i+1, result.Error)
				}
			}
		}
	})

	t.Run("HighConcurrencyStress", func(t *testing.T) {
		if testing.Short() {
			t.Skip("Skipping high concurrency test in short mode")
		}
		t.Parallel()

		numRequests := 100
		var wg sync.WaitGroup
		successChannel := make(chan bool, numRequests)

		start := time.Now()

		for i := 0; i < numRequests; i++ {
			wg.Add(1)
			go func(index int) {
				defer wg.Done()

				response, responseTime, err := client.ExecuteCode(testdata.QuickTest)

				success := err == nil && response != nil && response.Success
				successChannel <- success

				// Log every 10th request for monitoring
				if index%10 == 0 {
					t.Logf("Stress test %d: success=%v, time=%v", index+1, success, responseTime)
				}
			}(i)
		}

		wg.Wait()
		close(successChannel)

		totalTime := time.Since(start)

		// Count successes
		successCount := 0
		for success := range successChannel {
			if success {
				successCount++
			}
		}

		successRate := float64(successCount) / float64(numRequests) * 100
		requestsPerSecond := float64(numRequests) / totalTime.Seconds()

		t.Logf("High concurrency stress test results:")
		t.Logf("  Total requests: %d", numRequests)
		t.Logf("  Successful: %d (%.1f%%)", successCount, successRate)
		t.Logf("  Total time: %v", totalTime)
		t.Logf("  Requests per second: %.1f", requestsPerSecond)

		// Under high stress, we allow lower success rate
		minStressSuccessRate := 60.0
		if successRate < minStressSuccessRate {
			t.Errorf("Stress test success rate %.1f%% below minimum %.1f%%",
				successRate, minStressSuccessRate)
		}
	})
}

func TestLoadTest(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping load test in short mode")
	}

	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("SustainedLoad", func(t *testing.T) {
		t.Parallel()

		duration := 30 * time.Second
		requestInterval := 200 * time.Millisecond

		var requestCount int
		var successCount int
		var totalResponseTime time.Duration

		start := time.Now()
		end := start.Add(duration)

		t.Logf("Starting sustained load test for %v with %v intervals", duration, requestInterval)

		for time.Now().Before(end) {
			requestStart := time.Now()
			response, responseTime, err := client.ExecuteCode(testdata.QuickTest)

			requestCount++
			totalResponseTime += responseTime

			if err == nil && response != nil && response.Success {
				successCount++
			}

			// Log progress every 10 requests
			if requestCount%10 == 0 {
				elapsed := time.Since(start)
				rate := float64(requestCount) / elapsed.Seconds()
				successRate := float64(successCount) / float64(requestCount) * 100
				avgTime := totalResponseTime / time.Duration(requestCount)

				t.Logf("Load test progress: %d requests in %v (%.1f req/s, %.1f%% success, avg: %v)",
					requestCount, elapsed, rate, successRate, avgTime)
			}

			// Wait for next request
			elapsed := time.Since(requestStart)
			if elapsed < requestInterval {
				time.Sleep(requestInterval - elapsed)
			}
		}

		totalDuration := time.Since(start)
		successRate := float64(successCount) / float64(requestCount) * 100
		avgResponseTime := totalResponseTime / time.Duration(requestCount)
		requestsPerSecond := float64(requestCount) / totalDuration.Seconds()

		t.Logf("Sustained load test results:")
		t.Logf("  Duration: %v", totalDuration)
		t.Logf("  Total requests: %d", requestCount)
		t.Logf("  Successful: %d (%.1f%%)", successCount, successRate)
		t.Logf("  Requests per second: %.1f", requestsPerSecond)
		t.Logf("  Average response time: %v", avgResponseTime)

		// Assertions for sustained load
		minLoadSuccessRate := 85.0
		if successRate < minLoadSuccessRate {
			t.Errorf("Load test success rate %.1f%% below minimum %.1f%%",
				successRate, minLoadSuccessRate)
		}

		maxAvgResponseTime := 5 * time.Second
		if avgResponseTime > maxAvgResponseTime {
			t.Errorf("Average response time %v exceeds maximum %v",
				avgResponseTime, maxAvgResponseTime)
		}
	})
}

func TestResponseTimeDistribution(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping response time test in short mode")
	}

	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("ResponseTimeAnalysis", func(t *testing.T) {
		t.Parallel()

		numSamples := 50
		responseTimes := make([]time.Duration, numSamples)

		t.Logf("Collecting %d response time samples...", numSamples)

		for i := 0; i < numSamples; i++ {
			response, responseTime, err := client.ExecuteCode(testdata.MediumTest)

			if err != nil || response == nil || !response.Success {
				t.Logf("Sample %d failed: %v", i+1, err)
				responseTimes[i] = 0
			} else {
				responseTimes[i] = responseTime
			}

			// Small delay between samples
			time.Sleep(100 * time.Millisecond)
		}

		// Calculate statistics
		var validTimes []time.Duration
		for _, rt := range responseTimes {
			if rt > 0 {
				validTimes = append(validTimes, rt)
			}
		}

		if len(validTimes) == 0 {
			t.Fatal("No valid response times collected")
		}

		// Sort for percentile calculations
		for i := 0; i < len(validTimes)-1; i++ {
			for j := i + 1; j < len(validTimes); j++ {
				if validTimes[i] > validTimes[j] {
					validTimes[i], validTimes[j] = validTimes[j], validTimes[i]
				}
			}
		}

		// Calculate statistics
		min := validTimes[0]
		max := validTimes[len(validTimes)-1]

		var total time.Duration
		for _, rt := range validTimes {
			total += rt
		}
		avg := total / time.Duration(len(validTimes))

		p50 := validTimes[len(validTimes)*50/100]
		p90 := validTimes[len(validTimes)*90/100]
		p95 := validTimes[len(validTimes)*95/100]
		p99 := validTimes[len(validTimes)*99/100]

		t.Logf("Response time distribution (n=%d):", len(validTimes))
		t.Logf("  Min: %v", min)
		t.Logf("  Max: %v", max)
		t.Logf("  Avg: %v", avg)
		t.Logf("  P50: %v", p50)
		t.Logf("  P90: %v", p90)
		t.Logf("  P95: %v", p95)
		t.Logf("  P99: %v", p99)

		// Performance assertions
		if p95 > 10*time.Second {
			t.Errorf("95th percentile response time %v exceeds 10s", p95)
		}

		if p50 > 5*time.Second {
			t.Errorf("50th percentile response time %v exceeds 5s", p50)
		}
	})
}

// Benchmark tests
func BenchmarkExecuteCode(b *testing.B) {
	client := NewTestClient()

	// Skip if server unavailable
	if _, _, err := client.GetHealth(); err != nil {
		b.Skipf("Server not available: %v", err)
	}

	b.Run("SimplePrint", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, _, err := client.ExecuteCode(testdata.SimplePrint)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("MathCalculation", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, _, err := client.ExecuteCode(testdata.MathCalculation)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("StringOperations", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, _, err := client.ExecuteCode(testdata.StringOperations)
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}

func BenchmarkConcurrentExecution(b *testing.B) {
	client := NewTestClient()

	// Skip if server unavailable
	if _, _, err := client.GetHealth(); err != nil {
		b.Skipf("Server not available: %v", err)
	}

	b.Run("Parallel10", func(b *testing.B) {
		b.SetParallelism(10)
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				_, _, err := client.ExecuteCode(testdata.QuickTest)
				if err != nil {
					b.Fatal(err)
				}
			}
		})
	})

	b.Run("Parallel20", func(b *testing.B) {
		b.SetParallelism(20)
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				_, _, err := client.ExecuteCode(testdata.QuickTest)
				if err != nil {
					b.Fatal(err)
				}
			}
		})
	})
}

// Test result structure for performance testing
type TestResult struct {
	Name         string
	Success      bool
	ResponseTime time.Duration
	ExecTime     time.Duration
	Output       string
	Error        string
}
