package testdata

// Basic functionality Python scripts
const (
	SimplePrint = `print("Hello from <PERSON><PERSON>!")`

	MathCalculation = `result = 15 * 4 + 8
print(f"计算结果: {result}")
print(f"类型: {type(result)}")`

	StringOperations = `text = "Go Code Executors"
print(f"原文: {text}")
print(f"大写: {text.upper()}")
print(f"长度: {len(text)}")`

	SimpleStringConcat = `a = "hello"
b = "world"
result = a + b
print(result)
print("String concatenation test passed")`

	ListProcessing = `numbers = [2, 4, 6, 8, 10]
print(f"列表: {numbers}")
print(f"求和: {sum(numbers)}")
print(f"最大值: {max(numbers)}")`

	DictionaryOperations = `data = {"name": "Docker", "版本": "latest", "port": 8080}
print(f"字典: {data}")
print(f"名称: {data['name']}")
print(f"版本: {data['版本']}")
print(f"键数量: {len(data)}")`
)

// Python package testing scripts
const (
	NumPyTest = `import numpy as np
arr = np.array([1, 3, 5, 7, 9])
print(f"NumPy版本: {np.__version__}")
print(f"数组: {arr}")
print(f"平均值: {np.mean(arr)}")
print(f"标准差: {np.std(arr):.2f}")`

	PandasTest = `import pandas as pd
data = {"Name": ["Alice", "Bob", "Charlie"], "Score": [85, 92, 78]}
df = pd.DataFrame(data)
print(f"Pandas版本: {pd.__version__}")
print("数据框:")
print(df)
print(f"平均分数: {df['Score'].mean()}")`

	RequestsTest = `import requests
print(f"Requests版本: {requests.__version__}")
session = requests.Session()
print("Session对象创建成功")
print("HTTP库功能正常")`

	MatplotlibTest = `import matplotlib
import matplotlib.pyplot as plt
print(f"Matplotlib版本: {matplotlib.__version__}")
x = [1, 2, 3, 4, 5]
y = [2, 4, 6, 8, 10]
print(f"数据点: x={x}, y={y}")
print("可视化库加载成功")`

	ComprehensivePackageTest = `import numpy as np
import pandas as pd
import requests
import matplotlib
from PIL import Image
import yaml
from bs4 import BeautifulSoup
import ujson

print("=== 预装包版本检查 ===")
print(f"NumPy: {np.__version__}")
print(f"Pandas: {pd.__version__}")
print(f"Requests: {requests.__version__}")
print(f"Matplotlib: {matplotlib.__version__}")
try:
    print(f"Pillow: {Image.__version__}")
except AttributeError:
    print("Pillow: 已安装")
print("✅ 所有主要包导入成功！")`

	DataProcessingWorkflow = `import numpy as np
import pandas as pd

# 生成测试数据
np.random.seed(42)
data = {
    'ID': list(range(1, 6)),
    'Value': np.random.randint(10, 100, 5),
    'Category': ['A', 'B', 'A', 'C', 'B']
}

# 创建DataFrame并分析
df = pd.DataFrame(data)
print("原始数据:")
print(df)

print(f"\n统计信息:")
print(f"平均值: {df['Value'].mean():.2f}")
print(f"最大值: {df['Value'].max()}")
print(f"最小值: {df['Value'].min()}")

# 分组统计
grouped = df.groupby('Category')['Value'].mean()
print(f"\n按类别分组平均值:")
for cat, avg in grouped.items():
    print(f"  {cat}: {avg:.2f}")
print("✅ 数据处理流程完成！")`
)

// Error testing scripts
const (
	SyntaxError = `print("缺少引号`

	RuntimeError = `result = 100 / 0
print(result)`

	ImportError = `import nonexistent_module
print("不应该执行到这里")`

	NameError = `print(undefined_variable)`

	TypeError = `result = "字符串" + 42`

	IndentationError = `if True:
print("缩进错误")`

	AttributeError = `s = "hello"
print(s.nonexistent_method())`
)

// Performance testing scripts
const (
	ComplexCalculation = `import numpy as np
import time

start_time = time.time()

# 数组计算
arr = np.random.random((500, 500))
result = np.linalg.eigvals(arr[:50, :50])

# 数学计算
total = sum(i**2 for i in range(5000))

calc_time = time.time() - start_time
print(f"计算用时: {calc_time:.3f}秒")
print(f"特征值数量: {len(result)}")
print(f"数学计算结果: {total}")
print("✅ 复杂计算完成")`

	ConcurrentTestScript = `import time
import random
time.sleep(0.05)
result = random.randint(1, 100)
print(f"并发测试结果: {result}")`

	QuickTest = `print("快速测试")`

	MediumTest = `import time
time.sleep(0.1)
print("中等速度测试")`

	SlowTest = `import time
time.sleep(0.5)
print("慢速测试")`
)

// Utility scripts
const (
	VersionInfo = `import sys
import platform
print(f"Python版本: {sys.version}")
print(f"平台: {platform.platform()}")
print(f"架构: {platform.architecture()}")`

	MemoryTest = `import sys
import gc

# 创建一些对象
data = list(range(10000))
print(f"列表长度: {len(data)}")
print(f"对象大小: {sys.getsizeof(data)} bytes")

# 垃圾回收
collected = gc.collect()
print(f"垃圾回收对象数: {collected}")
print("内存测试完成")`

	TimeoutTest = `import time
print("开始长时间运行测试...")
time.sleep(35)  # Should timeout at 30s
print("不应该看到这行输出")`

	LargeOutputTest = `for i in range(1000):
    print(f"行 {i+1}: 这是一个很长的输出行，用于测试大量输出的情况 - " + "x" * 50)`
)

// Authentication test data
const (
	SimpleAuthTest = `print("认证测试成功")`

	AuthFailureTest = `print("这应该不会执行")`
)

// Integration test scripts
const (
	FullWorkflowTest = `import numpy as np
import pandas as pd
import requests
import matplotlib.pyplot as plt

print("=== 集成测试开始 ===")

# 1. 数据生成
print("1. 生成测试数据...")
np.random.seed(42)
data = np.random.normal(0, 1, 1000)

# 2. 数据分析
print("2. 数据分析...")
df = pd.DataFrame({'values': data})
mean_val = df['values'].mean()
std_val = df['values'].std()
print(f"平均值: {mean_val:.3f}")
print(f"标准差: {std_val:.3f}")

# 3. 统计计算
print("3. 统计计算...")
above_mean = (df['values'] > mean_val).sum()
below_mean = (df['values'] <= mean_val).sum()
print(f"高于平均值: {above_mean}")
print(f"低于平均值: {below_mean}")

# 4. 网络库测试
print("4. 网络库功能...")
session = requests.Session()
print(f"Requests版本: {requests.__version__}")

print("=== 集成测试完成 ===")
print("✅ 所有组件正常工作")`
)
