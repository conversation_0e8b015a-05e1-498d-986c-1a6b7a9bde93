package test

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"net/http"
	"strings"
	"testing"
	"time"

	"code-sandbox/internal/config"
	"code-sandbox/internal/executor"
	"code-sandbox/internal/handler"
)

// TestSecurityBlocking tests that dangerous code is properly blocked
func TestSecurityBlocking(t *testing.T) {
	cfg := &config.Config{
		AuthToken:     "test-token",
		ServerPort:    "8080",
		WorkerCount:   1,
		QueueSize:     10,
		ExecTimeout:   30 * time.Second,
		MemoryLimitMB: 256,
		MaxCodeSize:   1024 * 1024,
		RateLimitRPS:  10,
		SecurityConfig: config.SecurityConfig{
			BlockedImports: []string{
				"os", "sys", "subprocess", "socket", "urllib",
				"ctypes", "multiprocessing", "threading",
				"importlib", "__builtin__", "builtins", "marshal",
			},
			RestrictedImports: []string{
				"numpy", "pandas", "matplotlib", "scipy",
			},
			Validation: config.ValidationConfig{
				EnableAST:         true,
				MaxComplexity:     10,
				MaxRecursionDepth: 100,
			},
			WhitelistMode: false,
		},
	}

	pool := executor.NewPool(cfg)
	_ = handler.NewExecuteHandler(pool, cfg) // Initialize but not used in this test

	tests := []struct {
		name         string
		code         string
		shouldBlock  bool
		expectedRisk string
	}{
		{
			name:         "Command Injection - os.system",
			code:         "import os; os.system('rm -rf /')",
			shouldBlock:  true,
			expectedRisk: "blocked_import",
		},
		{
			name:         "Command Injection - subprocess",
			code:         "import subprocess; subprocess.call(['rm', '-rf', '/'])",
			shouldBlock:  true,
			expectedRisk: "blocked_import",
		},
		{
			name:         "Code Injection - eval",
			code:         "eval('__import__(\"os\").system(\"rm -rf /\")')",
			shouldBlock:  true,
			expectedRisk: "code_injection",
		},
		{
			name:         "Code Injection - exec",
			code:         "exec('import os; os.system(\"whoami\")')",
			shouldBlock:  true,
			expectedRisk: "code_injection",
		},
		{
			name:         "Dynamic Import - __import__",
			code:         "__import__('os').system('ls')",
			shouldBlock:  true,
			expectedRisk: "dynamic_import",
		},
		{
			name:         "File Access - open",
			code:         "open('/etc/passwd', 'r').read()",
			shouldBlock:  false, // Medium risk, not blocked by default
			expectedRisk: "file_access",
		},
		{
			name:         "Network Access - socket",
			code:         "import socket; s = socket.socket()",
			shouldBlock:  true,
			expectedRisk: "blocked_import",
		},
		{
			name:         "Memory DoS - Large allocation",
			code:         "[0] * 10000000",
			shouldBlock:  false, // Medium risk, monitored but not blocked
			expectedRisk: "memory_allocation",
		},
		{
			name:         "CPU DoS - Infinite loop",
			code:         "while True: pass",
			shouldBlock:  false, // Medium risk, relies on timeout
			expectedRisk: "infinite_loop",
		},
		{
			name:         "Safe Code - Math operations",
			code:         "import math; result = math.sqrt(16); print(result)",
			shouldBlock:  false,
			expectedRisk: "safe",
		},
		{
			name:         "Restricted Import - numpy",
			code:         "import numpy as np; arr = np.array([1, 2, 3]); print(arr)",
			shouldBlock:  false, // Restricted but allowed
			expectedRisk: "restricted_import",
		},
		{
			name:         "Resource DoS - Large Memory Allocation",
			code:         "data = [0] * (200 * 1024 * 1024)  # Try to allocate 200MB",
			shouldBlock:  false, // Should be stopped by ulimit, not code validator
			expectedRisk: "memory_allocation",
		},
		{
			name:         "Resource DoS - Excessive Output",
			code:         "print('x' * (20 * 1024 * 1024))  # Try to print 20MB",
			shouldBlock:  false, // Should be stopped by ulimit file size limit
			expectedRisk: "safe",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			// Create job and execute
			job := executor.Job{
				ID:   "test-" + strings.ReplaceAll(test.name, " ", "-"),
				Code: test.code,
			}

			// Create executor and validate
			exec := executor.NewExecutor(cfg)
			result := exec.Execute(context.Background(), job)

			if test.shouldBlock {
				// Code should be blocked
				if result.Success {
					t.Errorf("Test %s: Expected code to be blocked, but it executed successfully", test.name)
				}
				if !strings.Contains(result.Error, "security policy violations") {
					t.Errorf("Test %s: Expected security policy violation error, got: %s", test.name, result.Error)
				}
				t.Logf("✓ Test %s: Code properly blocked - %s", test.name, result.Error)
			} else {
				// Code should execute (may have warnings)
				if !result.Success && strings.Contains(result.Error, "security policy violations") {
					t.Errorf("Test %s: Code was blocked unexpectedly: %s", test.name, result.Error)
				}
				t.Logf("✓ Test %s: Code executed as expected", test.name)
			}
		})
	}
}

// TestSecurityValidationPerformance tests validation performance
func TestSecurityValidationPerformance(t *testing.T) {
	securityCfg := &config.SecurityConfig{
		BlockedImports: []string{
			"os", "sys", "subprocess", "socket",
		},
		RestrictedImports: []string{
			"numpy", "pandas",
		},
		Validation: config.ValidationConfig{
			EnableAST:         true,
			MaxComplexity:     10,
			MaxRecursionDepth: 100,
		},
		WhitelistMode: false,
	}

	validator := executor.NewCodeValidator(securityCfg)

	testCodes := []string{
		"print('Hello World')",
		"import math; print(math.sqrt(16))",
		"for i in range(1000): print(i)",
		`
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

print(fibonacci(10))
`,
		`
import json
data = {"name": "test", "values": [1, 2, 3, 4, 5]}
json_string = json.dumps(data)
parsed = json.loads(json_string)
print(parsed)
`,
	}

	for i, code := range testCodes {
		start := time.Now()

		// Measure validation performance
		result := validator.ValidateCode(code)
		duration := time.Since(start)

		t.Logf("Validation %d: Duration=%v, Risk=%s, Violations=%d",
			i, duration, result.Risk.GetRiskDescription(), len(result.Violations))

		// Validation should be fast (under 50ms for reasonable code)
		if duration > 50*time.Millisecond {
			t.Errorf("Validation %d took too long: %v", i, duration)
		}
	}
}

// TestHTTPSecurityEndpoint tests security through HTTP API
func TestHTTPSecurityEndpoint(t *testing.T) {
	// Start a test server
	cfg := &config.Config{
		AuthToken:     "test-security-token",
		ServerPort:    "9999",
		WorkerCount:   2,
		QueueSize:     10,
		ExecTimeout:   30 * time.Second,
		MemoryLimitMB: 256,
		MaxCodeSize:   1024 * 1024,
		RateLimitRPS:  50,
	}

	dangerousTests := []struct {
		name string
		code string
	}{
		{
			name: "OS Command Injection",
			code: "import os; os.system('echo \"HACKED\" > /tmp/hacked.txt')",
		},
		{
			name: "Subprocess Shell Injection",
			code: "import subprocess; subprocess.call('cat /etc/passwd', shell=True)",
		},
		{
			name: "Eval Code Injection",
			code: "eval('__import__(\"subprocess\").call(\"whoami\", shell=True)')",
		},
	}

	for _, test := range dangerousTests {
		t.Run(test.name, func(t *testing.T) {
			// Encode the malicious code
			encodedCode := base64.StdEncoding.EncodeToString([]byte(test.code))

			// Create request body
			reqBody := map[string]string{
				"code_base64": encodedCode,
			}
			reqJSON, _ := json.Marshal(reqBody)

			// Test through direct handler (simulating HTTP request)
			result := testExecuteCode(t, cfg, string(reqJSON))

			// Verify the code was blocked
			if result["success"].(bool) {
				t.Errorf("Test %s: Malicious code was not blocked!", test.name)
			}

			if !strings.Contains(result["error"].(string), "security policy violations") {
				t.Errorf("Test %s: Expected security violation error, got: %s",
					test.name, result["error"])
			}

			t.Logf("✓ Test %s: Malicious code blocked successfully", test.name)
		})
	}
}

// testExecuteCode simulates HTTP request to execute handler
func testExecuteCode(t *testing.T, cfg *config.Config, requestBody string) map[string]interface{} {
	pool := executor.NewPool(cfg)
	handler := handler.NewExecuteHandler(pool, cfg)

	// Create a request
	req, err := http.NewRequest("POST", "/execute", strings.NewReader(requestBody))
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+cfg.AuthToken)

	// Create response recorder
	rr := &TestResponseRecorder{
		HeaderMap: make(http.Header),
		Body:      &strings.Builder{},
		Code:      200,
	}

	// Execute handler
	handler.Handle(rr, req)

	// Parse response
	var result map[string]interface{}
	if err := json.Unmarshal([]byte(rr.Body.String()), &result); err != nil {
		t.Logf("Response body: %s", rr.Body.String())
		t.Fatalf("Failed to parse response: %v", err)
	}

	return result
}

// TestResponseRecorder is a simple HTTP response recorder for testing
type TestResponseRecorder struct {
	Code      int
	HeaderMap http.Header
	Body      *strings.Builder
}

func (rr *TestResponseRecorder) Header() http.Header {
	return rr.HeaderMap
}

func (rr *TestResponseRecorder) Write(data []byte) (int, error) {
	return rr.Body.Write(data)
}

func (rr *TestResponseRecorder) WriteHeader(statusCode int) {
	rr.Code = statusCode
}

func (rr *TestResponseRecorder) String() string {
	return rr.Body.String()
}

// TestLegitimateCodeStillWorks ensures legitimate code continues to work
func TestLegitimateCodeStillWorks(t *testing.T) {
	cfg := &config.Config{
		ExecTimeout:   30 * time.Second,
		MemoryLimitMB: 256,
	}

	legitimateCodes := []struct {
		name     string
		code     string
		expected string
	}{
		{
			name:     "Basic Math",
			code:     "print(2 + 2)",
			expected: "4",
		},
		{
			name:     "String Operations",
			code:     "name = 'Python'; print(f'Hello, {name}!')",
			expected: "Hello, Python!",
		},
		{
			name:     "List Operations",
			code:     "nums = [1, 2, 3, 4, 5]; print(sum(nums))",
			expected: "15",
		},
		{
			name:     "Dictionary Operations",
			code:     "data = {'key': 'value'}; print(data['key'])",
			expected: "value",
		},
		{
			name:     "Function Definition",
			code:     "def greet(name): return f'Hi {name}'; print(greet('World'))",
			expected: "Hi World",
		},
	}

	exec := executor.NewExecutor(cfg)

	for _, test := range legitimateCodes {
		t.Run(test.name, func(t *testing.T) {
			job := executor.Job{
				ID:   "legitimate-" + strings.ReplaceAll(test.name, " ", "-"),
				Code: test.code,
			}

			result := exec.Execute(context.Background(), job)

			if !result.Success {
				t.Errorf("Test %s: Legitimate code failed: %s", test.name, result.Error)
				return
			}

			if !strings.Contains(result.Stdout+result.Stderr, test.expected) {
				t.Errorf("Test %s: Expected output '%s', got '%s'",
					test.name, test.expected, result.Stdout+result.Stderr)
				return
			}

			t.Logf("✓ Test %s: Code executed successfully", test.name)
		})
	}
}

// TestResourceLimits tests that ulimit resource limits are properly enforced
func TestResourceLimits(t *testing.T) {
	cfg := &config.Config{
		ExecTimeout:   60 * time.Second, // Longer timeout for resource limit tests
		MemoryLimitMB: 50,               // Lower memory limit for testing
		MaxCodeSize:   1024 * 1024,
	}

	exec := executor.NewExecutor(cfg)

	resourceTests := []struct {
		name        string
		code        string
		expectError bool
		errorType   string
		description string
	}{
		{
			name: "Memory Limit Enforcement",
			code: `
try:
    # Try to allocate more memory than the 50MB limit
    data = [0] * (80 * 1024 * 1024)  # 80MB
    print("Memory allocation succeeded")
except MemoryError:
    print("MemoryError: Allocation blocked by ulimit")
except Exception as e:
    print(f"Other error: {e}")
`,
			expectError: false, // The code should run, but allocation should fail
			errorType:   "",
			description: "Memory allocation should be blocked by ulimit",
		},
		{
			name: "File Size Limit Enforcement",
			code: `
try:
    # Try to create large output (exceeds 10MB file size limit)
    large_string = "x" * (15 * 1024 * 1024)  # 15MB
    print(large_string)
except Exception as e:
    print(f"Output limited: {e}")
`,
			expectError: false, // Should be truncated by ulimit
			errorType:   "",
			description: "Large output should be limited by ulimit file size",
		},
		{
			name: "Process Limit Enforcement",
			code: `
import subprocess
import sys
try:
    # Try to create many processes (should hit ulimit -u limit)
    processes = []
    for i in range(15):  # Try to create 15 processes (limit is 10)
        proc = subprocess.Popen([sys.executable, "-c", "import time; time.sleep(1)"])
        processes.append(proc)
    print(f"Created {len(processes)} processes")
    for proc in processes:
        proc.terminate()
        proc.wait()
except Exception as e:
    print(f"Process creation limited: {e}")
`,
			expectError: true, // subprocess import should be blocked
			errorType:   "security policy violations",
			description: "Should be blocked by security validator (subprocess import)",
		},
		{
			name: "Stack Limit Test",
			code: `
def recursive_function(depth):
    if depth > 10000:  # Try deep recursion
        return depth
    return recursive_function(depth + 1)

try:
    result = recursive_function(0)
    print(f"Recursion completed: {result}")
except RecursionError:
    print("RecursionError: Stack limit reached")
except Exception as e:
    print(f"Other error: {e}")
`,
			expectError: false,
			errorType:   "",
			description: "Deep recursion should be limited by ulimit stack size",
		},
	}

	for _, test := range resourceTests {
		t.Run(test.name, func(t *testing.T) {
			job := executor.Job{
				ID:   "resource-test-" + strings.ReplaceAll(test.name, " ", "-"),
				Code: test.code,
			}

			result := exec.Execute(context.Background(), job)

			if test.expectError {
				// Should be blocked by security policy
				if result.Success {
					t.Errorf("Test %s: Expected code to be blocked, but it executed successfully", test.name)
				}
				if !strings.Contains(result.Error, test.errorType) {
					t.Errorf("Test %s: Expected error type '%s', got: %s", test.name, test.errorType, result.Error)
				}
				t.Logf("✓ Test %s: Code properly blocked - %s", test.name, result.Error)
			} else {
				// Should execute but resource limits should apply
				if !result.Success {
					t.Logf("Test %s: Execution failed (expected for resource limits): %s", test.name, result.Error)
				}

				// Log output to verify resource limiting behavior
				t.Logf("Test %s: Output - %s", test.name, result.Stdout+result.Stderr)
				t.Logf("✓ Test %s: Resource limit test completed - %s", test.name, test.description)
			}
		})
	}
}
