package test

import (
	"encoding/json"
	"strconv"
	"strings"
	"testing"
	"time"
)

func TestHealthEndpoint(t *testing.T) {
	client := NewTestClient()

	t.Run("BasicHealthCheck", func(t *testing.T) {
		t.<PERSON>lle<PERSON>()
		statusCode, body, err := client.GetHealth()

		if err != nil {
			t.Fatalf("Health check failed: %v", err)
		}

		if statusCode != 200 {
			t.<PERSON><PERSON><PERSON>("Expected status code 200, got %d", statusCode)
		}

		// Health endpoint should return "OK" or similar simple response
		body = strings.TrimSpace(body)
		if body == "" {
			t.<PERSON>rror("Health endpoint returned empty response")
		}

		t.Logf("Health check passed: %s (status: %d)", body, statusCode)
	})

	t.Run("HealthResponseTime", func(t *testing.T) {
		t.<PERSON>()
		start := time.Now()
		statusCode, body, err := client.GetHealth()
		responseTime := time.Since(start)

		if err != nil {
			t.<PERSON>alf("Health check failed: %v", err)
		}

		// Health check should be fast (under 5 seconds)
		maxResponseTime := 5 * time.Second
		if responseTime > maxResponseTime {
			t.<PERSON><PERSON><PERSON>("Health check took too long: %v (max: %v)", responseTime, maxResponseTime)
		}

		if statusCode != 200 {
			t.Errorf("Expected status code 200, got %d", statusCode)
		}

		t.Logf("Health check completed in %v with response: %s", responseTime, strings.TrimSpace(body))
	})

	t.Run("MultipleHealthChecks", func(t *testing.T) {
		t.Parallel()
		numChecks := 5
		successCount := 0

		for i := 0; i < numChecks; i++ {
			statusCode, body, err := client.GetHealth()

			if err == nil && statusCode == 200 && strings.TrimSpace(body) != "" {
				successCount++
			} else {
				t.Logf("Health check %d failed: status=%d, error=%v, body=%s",
					i+1, statusCode, err, body)
			}

			// Small delay between checks
			time.Sleep(100 * time.Millisecond)
		}

		if successCount != numChecks {
			t.Errorf("Expected %d successful health checks, got %d", numChecks, successCount)
		}

		t.Logf("Multiple health checks: %d/%d successful", successCount, numChecks)
	})
}

func TestStatusEndpoint(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("BasicStatusCheck", func(t *testing.T) {
		t.Parallel()
		status, err := client.GetStatus()

		if err != nil {
			t.Fatalf("Status check failed: %v", err)
		}

		// Validate status fields based on project configuration
		if status.Workers <= 0 {
			t.Errorf("Expected positive worker count, got %d", status.Workers)
		}

		if status.QueueSize < 0 {
			t.Errorf("Expected non-negative queue size, got %d", status.QueueSize)
		}

		if status.Timeout == "" {
			t.Error("Expected non-empty timeout value")
		}

		if status.Timestamp == "" {
			t.Error("Expected non-empty timestamp")
		}

		t.Logf("Status check passed - Workers: %d, Queue: %d, Timeout: %s",
			status.Workers, status.QueueSize, status.Timeout)
	})

	t.Run("StatusFieldValidation", func(t *testing.T) {
		t.Parallel()
		status, err := client.GetStatus()

		if err != nil {
			t.Fatalf("Status check failed: %v", err)
		}

		// Validate worker count is reasonable (based on project defaults)
		expectedMinWorkers := 1
		expectedMaxWorkers := 100
		if status.Workers < expectedMinWorkers || status.Workers > expectedMaxWorkers {
			t.Errorf("Worker count %d outside expected range [%d, %d]",
				status.Workers, expectedMinWorkers, expectedMaxWorkers)
		}

		// Validate queue size is reasonable
		expectedMaxQueueSize := 10000
		if status.QueueSize > expectedMaxQueueSize {
			t.Errorf("Queue size %d exceeds expected maximum %d",
				status.QueueSize, expectedMaxQueueSize)
		}

		// Validate timeout format (should contain 's' for seconds)
		if !strings.Contains(status.Timeout, "s") {
			t.Errorf("Timeout format unexpected: %s (expected to contain 's')", status.Timeout)
		}

		// Try to parse timeout value
		timeoutStr := strings.TrimSuffix(status.Timeout, "s")
		if timeoutValue, err := strconv.Atoi(timeoutStr); err != nil {
			t.Errorf("Could not parse timeout value: %s", status.Timeout)
		} else {
			// Timeout should be reasonable (between 10s and 300s)
			if timeoutValue < 10 || timeoutValue > 300 {
				t.Errorf("Timeout value %ds outside reasonable range [10s, 300s]", timeoutValue)
			}
		}

		// Validate timestamp format
		if _, err := time.Parse(time.RFC3339, status.Timestamp); err != nil {
			// Try alternative formats
			if _, err := time.Parse("2006-01-02T15:04:05Z", status.Timestamp); err != nil {
				if _, err := time.Parse("2006-01-02 15:04:05", status.Timestamp); err != nil {
					t.Errorf("Could not parse timestamp: %s", status.Timestamp)
				}
			}
		}

		t.Logf("Status validation passed - All fields valid")
	})

	t.Run("StatusConsistency", func(t *testing.T) {
		t.Parallel()
		// Get status multiple times and check for consistency
		numChecks := 3
		statuses := make([]*StatusResponse, numChecks)

		for i := 0; i < numChecks; i++ {
			status, err := client.GetStatus()
			if err != nil {
				t.Fatalf("Status check %d failed: %v", i+1, err)
			}
			statuses[i] = status
			time.Sleep(500 * time.Millisecond)
		}

		// Workers and timeout should be consistent (static configuration)
		firstStatus := statuses[0]
		for i := 1; i < numChecks; i++ {
			if statuses[i].Workers != firstStatus.Workers {
				t.Errorf("Worker count inconsistent: check 1: %d, check %d: %d",
					firstStatus.Workers, i+1, statuses[i].Workers)
			}

			if statuses[i].Timeout != firstStatus.Timeout {
				t.Errorf("Timeout inconsistent: check 1: %s, check %d: %s",
					firstStatus.Timeout, i+1, statuses[i].Timeout)
			}
		}

		// Queue size can vary (dynamic), just log the values
		t.Logf("Queue sizes across checks: %v",
			func() []int {
				sizes := make([]int, numChecks)
				for i, s := range statuses {
					sizes[i] = s.QueueSize
				}
				return sizes
			}())

		t.Logf("Status consistency check passed")
	})
}

func TestStatusResponseTiming(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("StatusResponseTime", func(t *testing.T) {
		t.Parallel()
		start := time.Now()
		status, err := client.GetStatus()
		responseTime := time.Since(start)

		if err != nil {
			t.Fatalf("Status check failed: %v", err)
		}

		// Status check should be fast (under 2 seconds)
		maxResponseTime := 2 * time.Second
		if responseTime > maxResponseTime {
			t.Errorf("Status check took too long: %v (max: %v)", responseTime, maxResponseTime)
		}

		t.Logf("Status check completed in %v - Workers: %d, Queue: %d",
			responseTime, status.Workers, status.QueueSize)
	})

	t.Run("ConcurrentStatusChecks", func(t *testing.T) {
		if testing.Short() {
			t.Skip("Skipping concurrent test in short mode")
		}
		t.Parallel()

		numGoroutines := 10
		results := make(chan time.Duration, numGoroutines)
		errors := make(chan error, numGoroutines)

		// Start concurrent status checks
		for i := 0; i < numGoroutines; i++ {
			go func(index int) {
				start := time.Now()
				_, err := client.GetStatus()
				duration := time.Since(start)

				if err != nil {
					errors <- err
				} else {
					results <- duration
				}
			}(i)
		}

		// Collect results
		successCount := 0
		var totalTime time.Duration
		var maxTime time.Duration

		for i := 0; i < numGoroutines; i++ {
			select {
			case duration := <-results:
				successCount++
				totalTime += duration
				if duration > maxTime {
					maxTime = duration
				}
			case err := <-errors:
				t.Logf("Concurrent status check failed: %v", err)
			}
		}

		if successCount == 0 {
			t.Fatal("No concurrent status checks succeeded")
		}

		avgTime := totalTime / time.Duration(successCount)
		t.Logf("Concurrent status checks: %d/%d successful, avg: %v, max: %v",
			successCount, numGoroutines, avgTime, maxTime)

		// Average response time should still be reasonable
		if avgTime > 5*time.Second {
			t.Errorf("Average concurrent response time too high: %v", avgTime)
		}
	})
}

func TestStatusEndpointLoad(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping load test in short mode")
	}

	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("StatusUnderLoad", func(t *testing.T) {
		// Make some execution requests to put load on the server
		// then check that status endpoint still works

		// Start background load
		loadDone := make(chan bool)
		go func() {
			defer func() { loadDone <- true }()

			for i := 0; i < 5; i++ {
				_, _, _ = client.ExecuteCode("import time; time.sleep(1); print('load test')")
				time.Sleep(200 * time.Millisecond)
			}
		}()

		// Check status while under load
		time.Sleep(100 * time.Millisecond) // Let load start

		for i := 0; i < 10; i++ {
			start := time.Now()
			status, err := client.GetStatus()
			responseTime := time.Since(start)

			if err != nil {
				t.Errorf("Status check %d failed under load: %v", i+1, err)
				continue
			}

			// Status should still be responsive (under 5 seconds even under load)
			if responseTime > 5*time.Second {
				t.Errorf("Status check %d too slow under load: %v", i+1, responseTime)
			}

			// Queue size might be higher under load
			t.Logf("Status under load %d: Workers=%d, Queue=%d, ResponseTime=%v",
				i+1, status.Workers, status.QueueSize, responseTime)

			time.Sleep(300 * time.Millisecond)
		}

		// Wait for load to finish
		<-loadDone
		t.Logf("Status endpoint remained responsive under load")
	})
}

func TestStatusJSON(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("StatusJSONFormat", func(t *testing.T) {
		t.Parallel()
		// Make raw HTTP request to status endpoint to check JSON format
		resp, err := client.Client.Get(client.BaseURL + "/status")
		if err != nil {
			t.Fatalf("Status request failed: %v", err)
		}
		defer func() { _ = resp.Body.Close() }()

		if resp.StatusCode != 200 {
			t.Errorf("Expected status code 200, got %d", resp.StatusCode)
		}

		// Check Content-Type header
		contentType := resp.Header.Get("Content-Type")
		if !strings.Contains(strings.ToLower(contentType), "application/json") {
			t.Errorf("Expected JSON content type, got: %s", contentType)
		}

		// Parse JSON manually to verify structure
		var rawJSON map[string]interface{}
		if err := json.NewDecoder(resp.Body).Decode(&rawJSON); err != nil {
			t.Fatalf("Failed to decode JSON response: %v", err)
		}

		// Check required fields exist
		requiredFields := []string{"workers", "queue_size", "timeout", "timestamp"}
		for _, field := range requiredFields {
			if _, exists := rawJSON[field]; !exists {
				t.Errorf("Missing required field in JSON: %s", field)
			}
		}

		// Check field types
		if workers, ok := rawJSON["workers"].(float64); !ok {
			t.Error("Field 'workers' should be a number")
		} else if workers <= 0 {
			t.Error("Field 'workers' should be positive")
		}

		if queueSize, ok := rawJSON["queue_size"].(float64); !ok {
			t.Error("Field 'queue_size' should be a number")
		} else if queueSize < 0 {
			t.Error("Field 'queue_size' should be non-negative")
		}

		if timeout, ok := rawJSON["timeout"].(string); !ok {
			t.Error("Field 'timeout' should be a string")
		} else if timeout == "" {
			t.Error("Field 'timeout' should not be empty")
		}

		if timestamp, ok := rawJSON["timestamp"].(string); !ok {
			t.Error("Field 'timestamp' should be a string")
		} else if timestamp == "" {
			t.Error("Field 'timestamp' should not be empty")
		}

		t.Logf("Status JSON format validation passed")
	})
}

func TestEndpointAvailability(t *testing.T) {
	client := NewTestClient()

	t.Run("AllEndpointsAccessible", func(t *testing.T) {
		t.Parallel()

		// Test health endpoint
		healthStatus, healthBody, healthErr := client.GetHealth()
		if healthErr != nil || healthStatus != 200 {
			t.Errorf("Health endpoint not accessible: status=%d, error=%v", healthStatus, healthErr)
		} else {
			t.Logf("Health endpoint accessible: %s", strings.TrimSpace(healthBody))
		}

		// Test status endpoint
		status, statusErr := client.GetStatus()
		if statusErr != nil {
			t.Errorf("Status endpoint not accessible: %v", statusErr)
		} else {
			t.Logf("Status endpoint accessible: Workers=%d, Queue=%d", status.Workers, status.QueueSize)
		}

		// Test execute endpoint (basic check)
		response, responseTime, execErr := client.ExecuteCode("print('endpoint test')")
		if execErr != nil {
			t.Errorf("Execute endpoint not accessible: %v", execErr)
		} else if !response.Success {
			t.Errorf("Execute endpoint not working: %s", response.Error)
		} else {
			t.Logf("Execute endpoint accessible and working in %v", responseTime)
		}
	})
}
