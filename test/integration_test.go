//go:build integration
// +build integration

package test

import (
	"strings"
	"sync"
	"testing"
	"time"

	"code-sandbox/test/testdata"
)

func TestFullIntegration(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("CompleteWorkflow", func(t *testing.T) {
		// Test complete workflow: health -> status -> auth -> execute -> error handling

		// 1. Health check
		t.Log("Step 1: Health check")
		statusCode, body, err := client.GetHealth()
		if err != nil || statusCode != 200 {
			t.Fatalf("Health check failed: status=%d, error=%v", statusCode, err)
		}
		t.Logf("Health check passed: %s", strings.TrimSpace(body))

		// 2. Status check
		t.Log("Step 2: Status check")
		status, err := client.GetStatus()
		if err != nil {
			t.Fatalf("Status check failed: %v", err)
		}
		t.Logf("Status: Workers=%d, Queue=%d, Timeout=%s",
			status.Workers, status.QueueSize, status.Timeout)

		// 3. Authentication test
		t.Log("Step 3: Authentication test")
		response, responseTime, err := client.ExecuteCode(testdata.SimpleAuthTest)
		AssertSuccess(t, response, err, responseTime)
		t.Logf("Authentication successful in %v", responseTime)

		// 4. Basic execution
		t.Log("Step 4: Basic code execution")
		response, responseTime, err = client.ExecuteCode(testdata.SimplePrint)
		AssertSuccess(t, response, err, responseTime)
		AssertOutputContains(t, response, "Hello from Docker!")
		t.Logf("Basic execution successful in %v", responseTime)

		// 5. Package functionality
		t.Log("Step 5: Package functionality test")
		response, responseTime, err = client.ExecuteCode(testdata.NumPyTest)
		AssertSuccess(t, response, err, responseTime)
		AssertOutputContains(t, response, "NumPy版本:")
		t.Logf("Package test successful in %v", responseTime)

		// 6. Error handling
		t.Log("Step 6: Error handling test")
		response, responseTime, err = client.ExecuteCode(testdata.SyntaxError)
		if err == nil && response.Success {
			t.Fatal("Expected error handling but execution succeeded")
		}
		t.Logf("Error handling working correctly in %v", responseTime)

		// 7. Recovery after error
		t.Log("Step 7: Recovery after error")
		response, responseTime, err = client.ExecuteCode(testdata.MathCalculation)
		AssertSuccess(t, response, err, responseTime)
		AssertOutputContains(t, response, "计算结果: 68")
		t.Logf("Recovery after error successful in %v", responseTime)

		t.Log("Complete workflow test passed!")
	})

	t.Run("FullWorkflowDataProcessing", func(t *testing.T) {
		// Execute the comprehensive integration test script
		response, responseTime, err := client.ExecuteCode(testdata.FullWorkflowTest)
		AssertSuccess(t, response, err, responseTime)

		// Verify all workflow steps completed
		expectedOutputs := []string{
			"=== 集成测试开始 ===",
			"1. 生成测试数据...",
			"2. 数据分析...",
			"3. 统计计算...",
			"4. 网络库功能...",
			"=== 集成测试完成 ===",
			"✅ 所有组件正常工作",
		}

		for _, expected := range expectedOutputs {
			AssertOutputContains(t, response, expected)
		}

		// Should contain statistical results
		AssertOutputContains(t, response, "平均值:")
		AssertOutputContains(t, response, "标准差:")
		AssertOutputContains(t, response, "高于平均值:")
		AssertOutputContains(t, response, "Requests版本:")

		t.Logf("Full workflow data processing completed in %v", responseTime)
	})
}

func TestEndToEndScenarios(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping end-to-end scenarios in short mode")
	}

	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("DataScienceWorkflow", func(t *testing.T) {
		t.Parallel()

		// Simulate a data science workflow
		steps := []struct {
			name        string
			code        string
			description string
		}{
			{
				name:        "DataGeneration",
				description: "Generate synthetic dataset",
				code: `
import numpy as np
import pandas as pd

np.random.seed(42)
print("=== 数据科学工作流 ===")
print("步骤 1: 生成合成数据集")

# 生成用户数据
n_users = 1000
user_data = {
    'user_id': range(1, n_users + 1),
    'age': np.random.randint(18, 80, n_users),
    'income': np.random.normal(50000, 20000, n_users),
    'spending': np.random.normal(2000, 800, n_users),
    'satisfaction': np.random.uniform(1, 10, n_users)
}

df = pd.DataFrame(user_data)
df['income'] = np.maximum(df['income'], 20000)  # 最低收入
df['spending'] = np.maximum(df['spending'], 0)  # 非负支出

print(f"生成数据集: {df.shape[0]} 用户, {df.shape[1]} 特征")
print(f"年龄范围: {df['age'].min()} - {df['age'].max()}")
print(f"收入范围: ${df['income'].min():.0f} - ${df['income'].max():.0f}")
print("✅ 数据生成完成")
`,
			},
			{
				name:        "DataAnalysis",
				description: "Perform data analysis",
				code: `
import numpy as np
import pandas as pd

# 重新生成相同的数据（保持一致性）
np.random.seed(42)
n_users = 1000
user_data = {
    'user_id': range(1, n_users + 1),
    'age': np.random.randint(18, 80, n_users),
    'income': np.random.normal(50000, 20000, n_users),
    'spending': np.random.normal(2000, 800, n_users),
    'satisfaction': np.random.uniform(1, 10, n_users)
}

df = pd.DataFrame(user_data)
df['income'] = np.maximum(df['income'], 20000)
df['spending'] = np.maximum(df['spending'], 0)

print("步骤 2: 数据分析")

# 基本统计
print(f"平均年龄: {df['age'].mean():.1f} 岁")
print(f"平均收入: ${df['income'].mean():.0f}")
print(f"平均支出: ${df['spending'].mean():.0f}")
print(f"平均满意度: {df['satisfaction'].mean():.2f}/10")

# 相关性分析
income_spending_corr = df['income'].corr(df['spending'])
age_satisfaction_corr = df['age'].corr(df['satisfaction'])

print(f"收入与支出相关性: {income_spending_corr:.3f}")
print(f"年龄与满意度相关性: {age_satisfaction_corr:.3f}")

print("✅ 数据分析完成")
`,
			},
			{
				name:        "ModelingPreparation",
				description: "Prepare data for modeling",
				code: `
import numpy as np
import pandas as pd

# 重新生成数据
np.random.seed(42)
n_users = 1000
user_data = {
    'user_id': range(1, n_users + 1),
    'age': np.random.randint(18, 80, n_users),
    'income': np.random.normal(50000, 20000, n_users),
    'spending': np.random.normal(2000, 800, n_users),
    'satisfaction': np.random.uniform(1, 10, n_users)
}

df = pd.DataFrame(user_data)
df['income'] = np.maximum(df['income'], 20000)
df['spending'] = np.maximum(df['spending'], 0)

print("步骤 3: 建模准备")

# 创建特征
df['income_bracket'] = pd.cut(df['income'], bins=3, labels=['低', '中', '高'])
df['age_group'] = pd.cut(df['age'], bins=[18, 35, 50, 80], labels=['青年', '中年', '老年'])
df['spending_ratio'] = df['spending'] / df['income']

print("创建的新特征:")
print(f"  收入等级分布: {df['income_bracket'].value_counts().to_dict()}")
print(f"  年龄组分布: {df['age_group'].value_counts().to_dict()}")
print(f"  支出比例范围: {df['spending_ratio'].min():.3f} - {df['spending_ratio'].max():.3f}")

# 数据质量检查
print(f"缺失值检查: {df.isnull().sum().sum()} 个缺失值")
print(f"异常值检查: 支出比例 > 1.0 的用户: {(df['spending_ratio'] > 1.0).sum()} 个")

print("✅ 建模准备完成")
`,
			},
		}

		for _, step := range steps {
			t.Run(step.name, func(t *testing.T) {
				t.Logf("Executing: %s", step.description)

				response, responseTime, err := client.ExecuteCode(step.code)
				AssertSuccess(t, response, err, responseTime)

				// Check for success indicators
				AssertOutputContains(t, response, "✅")
				AssertOutputContains(t, response, step.name[:4]) // First part of step name

				t.Logf("%s completed in %v", step.name, responseTime)
			})
		}
	})

	t.Run("WebScrapingWorkflow", func(t *testing.T) {
		t.Parallel()

		// Test web scraping capabilities using available packages
		code := `
import requests
from bs4 import BeautifulSoup
import pandas as pd

print("=== 网页抓取工作流 ===")

# 模拟网页内容（因为我们不能访问真实网页）
mock_html = """
<html>
<head><title>产品目录</title></head>
<body>
    <div class="product-list">
        <div class="product">
            <h3 class="name">产品 A</h3>
            <span class="price">$99.99</span>
            <div class="rating">4.5</div>
        </div>
        <div class="product">
            <h3 class="name">产品 B</h3>
            <span class="price">$149.99</span>
            <div class="rating">4.2</div>
        </div>
        <div class="product">
            <h3 class="name">产品 C</h3>
            <span class="price">$79.99</span>
            <div class="rating">4.8</div>
        </div>
    </div>
</body>
</html>
"""

print("步骤 1: 解析HTML内容")
soup = BeautifulSoup(mock_html, 'html.parser')

# 提取产品信息
products = []
for product in soup.find_all('div', class_='product'):
    name = product.find('h3', class_='name').text
    price = product.find('span', class_='price').text
    rating = product.find('div', class_='rating').text
    
    products.append({
        'name': name,
        'price': price,
        'rating': float(rating)
    })

print(f"提取了 {len(products)} 个产品")

print("步骤 2: 数据处理")
df = pd.DataFrame(products)

# 清理价格数据
df['price_numeric'] = df['price'].str.replace('$', '').astype(float)

print("产品数据:")
print(df)

print("步骤 3: 数据分析")
print(f"平均价格: ${df['price_numeric'].mean():.2f}")
print(f"平均评分: {df['rating'].mean():.2f}")
print(f"最高评分产品: {df.loc[df['rating'].idxmax(), 'name']}")
print(f"最便宜产品: {df.loc[df['price_numeric'].idxmin(), 'name']}")

print("✅ 网页抓取工作流完成")
`

		response, responseTime, err := client.ExecuteCode(code)
		AssertSuccess(t, response, err, responseTime)

		AssertOutputContains(t, response, "=== 网页抓取工作流 ===")
		AssertOutputContains(t, response, "提取了 3 个产品")
		AssertOutputContains(t, response, "平均价格:")
		AssertOutputContains(t, response, "✅ 网页抓取工作流完成")

		t.Logf("Web scraping workflow completed in %v", responseTime)
	})
}

func TestMultiUserScenario(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping multi-user scenario in short mode")
	}

	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("SimultaneousUsers", func(t *testing.T) {
		numUsers := 5
		var wg sync.WaitGroup
		results := make(chan string, numUsers)

		// Simulate different users doing different tasks
		userTasks := []struct {
			name string
			code string
		}{
			{"DataScientist", testdata.DataProcessingWorkflow},
			{"WebDeveloper", testdata.RequestsTest},
			{"Mathematician", testdata.ComplexCalculation},
			{"Researcher", testdata.NumPyTest},
			{"Analyst", testdata.PandasTest},
		}

		t.Logf("Starting simulation of %d simultaneous users", numUsers)

		for i := 0; i < numUsers; i++ {
			wg.Add(1)
			go func(userIndex int) {
				defer wg.Done()

				task := userTasks[userIndex%len(userTasks)]
				userClient := NewTestClientWithConfig(client.BaseURL, client.AuthToken)

				start := time.Now()
				response, responseTime, err := userClient.ExecuteCode(task.code)

				result := fmt.Sprintf("User%d (%s): ", userIndex+1, task.name)
				if err != nil {
					result += fmt.Sprintf("FAILED - %v (time: %v)", err, responseTime)
				} else if !response.Success {
					result += fmt.Sprintf("FAILED - %s (time: %v)", response.Error, responseTime)
				} else {
					result += fmt.Sprintf("SUCCESS (time: %v, exec: %dms)", responseTime, response.ExecTimeMs)
				}

				totalTime := time.Since(start)
				result += fmt.Sprintf(" [total: %v]", totalTime)

				results <- result
			}(i)
		}

		wg.Wait()
		close(results)

		// Collect and report results
		successCount := 0
		for result := range results {
			t.Log(result)
			if strings.Contains(result, "SUCCESS") {
				successCount++
			}
		}

		successRate := float64(successCount) / float64(numUsers) * 100
		t.Logf("Multi-user simulation results: %d/%d users successful (%.1f%%)",
			successCount, numUsers, successRate)

		// At least 80% should succeed
		if successRate < 80.0 {
			t.Errorf("Multi-user success rate %.1f%% below minimum 80%%", successRate)
		}
	})
}

func TestSystemResilience(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping resilience test in short mode")
	}

	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("ErrorRecoveryResilience", func(t *testing.T) {
		// Test system resilience with alternating success/error patterns
		testSequence := []struct {
			name          string
			code          string
			shouldSucceed bool
		}{
			{"Success1", testdata.SimplePrint, true},
			{"Error1", testdata.SyntaxError, false},
			{"Success2", testdata.MathCalculation, true},
			{"Error2", testdata.RuntimeError, false},
			{"Success3", testdata.StringOperations, true},
			{"Error3", testdata.ImportError, false},
			{"Success4", testdata.ListProcessing, true},
			{"Error4", testdata.NameError, false},
			{"FinalSuccess", testdata.ComprehensivePackageTest, true},
		}

		for i, test := range testSequence {
			t.Run(test.name, func(t *testing.T) {
				t.Logf("Resilience test %d/%d: %s", i+1, len(testSequence), test.name)

				response, responseTime, err := client.ExecuteCode(test.code)

				if test.shouldSucceed {
					if err != nil || (response != nil && !response.Success) {
						t.Errorf("Expected success but got failure: err=%v, success=%v",
							err, response != nil && response.Success)
					} else {
						t.Logf("Success case passed in %v", responseTime)
					}
				} else {
					if err == nil && response != nil && response.Success {
						t.Errorf("Expected failure but got success")
					} else {
						t.Logf("Error case handled correctly in %v", responseTime)
					}
				}

				// Small delay between tests
				time.Sleep(100 * time.Millisecond)
			})
		}

		t.Log("System resilience test completed - system handled alternating success/error patterns")
	})

	t.Run("ResourceExhaustionRecovery", func(t *testing.T) {
		// Test recovery after attempting resource-intensive operations
		t.Log("Testing resource exhaustion and recovery...")

		// Attempt memory-intensive operation (might fail due to limits)
		response1, responseTime1, err1 := client.ExecuteCode(testdata.MemoryTest)
		t.Logf("Memory test result: success=%v, time=%v, err=%v",
			response1 != nil && response1.Success, responseTime1, err1)

		// System should still work for normal operations after memory test
		time.Sleep(1 * time.Second) // Give system time to recover

		response2, responseTime2, err2 := client.ExecuteCode(testdata.SimplePrint)
		AssertSuccess(t, response2, err2, responseTime2)
		AssertOutputContains(t, response2, "Hello from Docker!")

		t.Logf("Recovery test passed: system functional after resource test (time: %v)", responseTime2)
	})
}

func TestIntegrationEdgeCases(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("LargeOutputHandling", func(t *testing.T) {
		if testing.Short() {
			t.Skip("Skipping large output test in short mode")
		}
		t.Parallel()

		// Test handling of large output
		response, responseTime, err := client.ExecuteCode(testdata.LargeOutputTest)
		AssertSuccess(t, response, err, responseTime)

		// Should handle large output gracefully
		if len(response.Output) == 0 {
			t.Error("Expected large output but got empty response")
		}

		// Output should contain multiple lines
		lines := strings.Split(response.Output, "\n")
		if len(lines) < 100 {
			t.Errorf("Expected at least 100 lines but got %d", len(lines))
		}

		t.Logf("Large output test completed: %d characters, %d lines, time: %v",
			len(response.Output), len(lines), responseTime)
	})

	t.Run("UnicodeAndInternationalization", func(t *testing.T) {
		t.Parallel()

		code := `
# 测试Unicode和国际化支持
import sys

print("=== Unicode和国际化测试 ===")
print(f"系统编码: {sys.getdefaultencoding()}")

# 中文文本
chinese_text = "这是中文测试：数据科学、人工智能、机器学习"
print(f"中文: {chinese_text}")

# 日文文本
japanese_text = "これは日本語のテストです：データサイエンス"
print(f"日语: {japanese_text}")

# 阿拉伯文文本
arabic_text = "هذا اختبار باللغة العربية"
print(f"阿拉伯语: {arabic_text}")

# Emoji表情符号
emojis = "🐍 Python 🔬 科学 📊 数据 🤖 AI"
print(f"Emoji: {emojis}")

# 数学符号
math_symbols = "π ≈ 3.14159, ∑, ∫, √, ∞, α, β, γ"
print(f"数学符号: {math_symbols}")

# 特殊字符处理
special_chars = "Ñoël, naïve, résumé, café"
print(f"特殊字符: {special_chars}")

print("✅ Unicode测试完成")
`

		response, responseTime, err := client.ExecuteCode(code)
		AssertSuccess(t, response, err, responseTime)

		// Verify Unicode content is preserved
		AssertOutputContains(t, response, "这是中文测试")
		AssertOutputContains(t, response, "これは日本語")
		AssertOutputContains(t, response, "🐍")
		AssertOutputContains(t, response, "π")
		AssertOutputContains(t, response, "✅ Unicode测试完成")

		t.Logf("Unicode and internationalization test passed in %v", responseTime)
	})

	t.Run("ComplexDataStructures", func(t *testing.T) {
		t.Parallel()

		code := `
import json
import pickle
import base64

print("=== 复杂数据结构测试 ===")

# 嵌套字典和列表
complex_data = {
    'users': [
        {
            'id': 1,
            'name': '张三',
            'profile': {
                'age': 30,
                'skills': ['Python', 'Data Science', 'Machine Learning'],
                'projects': [
                    {'name': '项目A', 'status': '完成', 'score': 9.5},
                    {'name': '项目B', 'status': '进行中', 'score': 8.2}
                ]
            },
            'metadata': {
                'created': '2023-01-15',
                'updated': '2024-03-20',
                'tags': ['senior', 'expert', 'leader']
            }
        },
        {
            'id': 2,
            'name': '李四',
            'profile': {
                'age': 25,
                'skills': ['JavaScript', 'React', 'Node.js'],
                'projects': [
                    {'name': '项目C', 'status': '完成', 'score': 8.8}
                ]
            }
        }
    ],
    'statistics': {
        'total_users': 2,
        'avg_age': 27.5,
        'skill_distribution': {
            'Python': 1,
            'JavaScript': 1,
            'Data Science': 1,
            'React': 1
        }
    }
}

print(f"数据结构深度: {len(str(complex_data))} 字符")
print(f"用户数量: {len(complex_data['users'])}")

# JSON序列化测试
json_str = json.dumps(complex_data, ensure_ascii=False, indent=2)
json_parsed = json.loads(json_str)

print(f"JSON序列化成功: {len(json_str)} 字符")
print(f"JSON解析验证: {json_parsed['users'][0]['name']}")

# 数据查询测试
python_users = [u for u in complex_data['users'] if 'Python' in u['profile']['skills']]
print(f"Python用户: {len(python_users)} 个")

# 复杂计算
total_projects = sum(len(u['profile']['projects']) for u in complex_data['users'])
avg_score = sum(p['score'] for u in complex_data['users'] 
                for p in u['profile']['projects']) / total_projects

print(f"总项目数: {total_projects}")
print(f"平均项目评分: {avg_score:.2f}")

print("✅ 复杂数据结构测试完成")
`

		response, responseTime, err := client.ExecuteCode(code)
		AssertSuccess(t, response, err, responseTime)

		AssertOutputContains(t, response, "JSON序列化成功:")
		AssertOutputContains(t, response, "Python用户: 1 个")
		AssertOutputContains(t, response, "平均项目评分:")
		AssertOutputContains(t, response, "✅ 复杂数据结构测试完成")

		t.Logf("Complex data structures test passed in %v", responseTime)
	})
}
