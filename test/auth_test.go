package test

import (
	"strings"
	"testing"
	"time"

	"code-sandbox/test/testdata"
)

func TestAuthentication(t *testing.T) {
	client := NewTestClient()
	originalToken := client.AuthToken
	SkipIfServerUnavailable(t, client)

	t.Run("ValidToken", func(t *testing.T) {
		t.Parallel()
		// Use a separate client to avoid modifying the shared one
		validClient := NewTestClientWithConfig(client.BaseURL, originalToken)

		response, responseTime, err := validClient.ExecuteCode(testdata.SimpleAuthTest)
		AssertSuccess(t, response, err, responseTime)
		AssertOutputContains(t, response, "认证测试成功")
		AssertResponseTime(t, responseTime, 10*time.Second)

		t.Logf("Valid token test completed in %v", responseTime)
	})

	t.Run("InvalidToken", func(t *testing.T) {
		t.Parallel()
		// Create client with invalid token
		invalidToken := CreateTempToken()
		invalidClient := NewTestClientWithConfig(client.BaseURL, invalidToken)

		_, responseTime, err := invalidClient.ExecuteCode(testdata.AuthFailureTest)

		// Should fail with authentication error
		if err == nil {
			t.Fatal("Expected authentication failure but request succeeded")
		}

		// Check that error message indicates authentication problem
		errorMsg := strings.ToLower(err.Error())
		if !strings.Contains(errorMsg, "401") && !strings.Contains(errorMsg, "unauthorized") && !strings.Contains(errorMsg, "forbidden") {
			t.Errorf("Expected authentication error, got: %v", err)
		}

		AssertResponseTime(t, responseTime, 10*time.Second)
		t.Logf("Invalid token correctly rejected in %v with error: %v", responseTime, err)
	})

	t.Run("MissingToken", func(t *testing.T) {
		t.Parallel()
		// Create client with empty token
		emptyClient := NewTestClientWithConfig(client.BaseURL, "")

		_, responseTime, err := emptyClient.ExecuteCode(testdata.AuthFailureTest)

		// Should fail with authentication error
		if err == nil {
			t.Fatal("Expected authentication failure for missing token but request succeeded")
		}

		AssertResponseTime(t, responseTime, 10*time.Second)
		t.Logf("Missing token correctly rejected in %v", responseTime)
	})

	t.Run("MalformedToken", func(t *testing.T) {
		t.Parallel()
		malformedTokens := []string{
			"Bearer invalid-token", // Double Bearer prefix
			"not-a-bearer-token",
			"token-with-spaces token",
			"token\nwith\nnewlines",
			"token\twith\ttabs",
			strings.Repeat("x", 1000), // Very long token
		}

		for i, malformedToken := range malformedTokens {
			t.Run(string(rune('A'+i)), func(t *testing.T) {
				t.Parallel()
				malformedClient := NewTestClientWithConfig(client.BaseURL, malformedToken)

				_, responseTime, err := malformedClient.ExecuteCode(testdata.AuthFailureTest)

				// Should fail with authentication error
				if err == nil {
					t.Errorf("Expected authentication failure for malformed token %q but request succeeded", malformedToken)
				}

				AssertResponseTime(t, responseTime, 10*time.Second)
				t.Logf("Malformed token %q correctly rejected in %v", malformedToken[:min(20, len(malformedToken))], responseTime)
			})
		}
	})

	t.Run("TokenRecovery", func(t *testing.T) {
		// This test ensures that after failed authentication,
		// subsequent requests with valid tokens work correctly

		// First, try with invalid token
		invalidClient := NewTestClientWithConfig(client.BaseURL, "invalid-token-123")
		_, responseTime1, err1 := invalidClient.ExecuteCode(testdata.AuthFailureTest)

		if err1 == nil {
			t.Fatal("Expected first request with invalid token to fail")
		}

		t.Logf("Invalid token request failed as expected in %v", responseTime1)

		// Then, try with valid token using same client instance (but different token)
		validClient := NewTestClientWithConfig(client.BaseURL, originalToken)
		response2, responseTime2, err2 := validClient.ExecuteCode(testdata.SimpleAuthTest)

		AssertSuccess(t, response2, err2, responseTime2)
		AssertOutputContains(t, response2, "认证测试成功")

		t.Logf("Token recovery successful in %v", responseTime2)
	})
}

func TestAuthenticationHeaders(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("BearerTokenFormat", func(t *testing.T) {
		t.Parallel()
		// The client should automatically add "Bearer " prefix
		// Let's test with a custom token to ensure the format is correct
		customToken := "test-bearer-token-format-123"
		bearerClient := NewTestClientWithConfig(client.BaseURL, customToken)

		response, responseTime, err := bearerClient.ExecuteCode(testdata.SimpleAuthTest)

		// This should work if the Bearer format is handled correctly
		AssertSuccess(t, response, err, responseTime)
		AssertOutputContains(t, response, "认证测试成功")

		t.Logf("Bearer token format test completed in %v", responseTime)
	})

	t.Run("CaseSensitivity", func(t *testing.T) {
		t.Parallel()
		// Test if authentication is case-sensitive
		originalToken := client.AuthToken
		upperCaseToken := strings.ToUpper(originalToken)
		lowerCaseToken := strings.ToLower(originalToken)

		// Test uppercase version
		upperClient := NewTestClientWithConfig(client.BaseURL, upperCaseToken)
		_, _, err1 := upperClient.ExecuteCode(testdata.SimpleAuthTest)

		// Test lowercase version
		lowerClient := NewTestClientWithConfig(client.BaseURL, lowerCaseToken)
		_, _, err2 := lowerClient.ExecuteCode(testdata.SimpleAuthTest)

		// At least one of these should fail if tokens are case-sensitive
		// The behavior depends on the server implementation
		if err1 == nil && err2 == nil && upperCaseToken != originalToken && lowerCaseToken != originalToken {
			t.Log("Tokens appear to be case-insensitive")
		} else {
			t.Log("Tokens appear to be case-sensitive (expected)")
		}
	})
}

func TestRateLimitingWithAuth(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping rate limiting test in short mode")
	}

	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("RapidRequestsWithValidAuth", func(t *testing.T) {
		// Make several rapid requests to test rate limiting
		numRequests := 20
		successCount := 0
		rateLimitCount := 0

		for i := 0; i < numRequests; i++ {
			response, responseTime, err := client.ExecuteCode(testdata.QuickTest)

			if err != nil {
				// Check if it's a rate limiting error
				errorMsg := strings.ToLower(err.Error())
				if strings.Contains(errorMsg, "429") || strings.Contains(errorMsg, "rate limit") || strings.Contains(errorMsg, "too many") {
					rateLimitCount++
					t.Logf("Request %d rate limited in %v", i+1, responseTime)
				} else {
					t.Logf("Request %d failed with non-rate-limit error: %v", i+1, err)
				}
			} else if response.Success {
				successCount++
				t.Logf("Request %d succeeded in %v", i+1, responseTime)
			} else {
				t.Logf("Request %d failed with execution error: %s", i+1, response.Error)
			}

			// Small delay to avoid overwhelming the server
			time.Sleep(50 * time.Millisecond)
		}

		t.Logf("Rate limiting test results: %d successful, %d rate limited, %d total",
			successCount, rateLimitCount, numRequests)

		// We should have at least some successful requests
		if successCount == 0 {
			t.Error("Expected at least some requests to succeed")
		}
	})

	t.Run("RateLimitingWithInvalidAuth", func(t *testing.T) {
		// Test that rate limiting still applies to invalid authentication attempts
		invalidClient := NewTestClientWithConfig(client.BaseURL, "invalid-rate-limit-test-token")

		for i := 0; i < 5; i++ {
			_, responseTime, err := invalidClient.ExecuteCode(testdata.AuthFailureTest)

			// All should fail, but check response time is reasonable
			if err == nil {
				t.Error("Expected authentication failure")
			}

			AssertResponseTime(t, responseTime, 10*time.Second)

			// Small delay
			time.Sleep(100 * time.Millisecond)
		}
	})
}

func TestConcurrentAuthentication(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping concurrent authentication test in short mode")
	}

	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("ConcurrentValidRequests", func(t *testing.T) {
		t.Parallel()
		numGoroutines := 10
		results := make(chan bool, numGoroutines)

		// Start multiple goroutines making authenticated requests
		for i := 0; i < numGoroutines; i++ {
			go func(index int) {
				defer func() {
					if r := recover(); r != nil {
						t.Errorf("Goroutine %d panicked: %v", index, r)
						results <- false
						return
					}
				}()

				concurrentClient := NewTestClientWithConfig(client.BaseURL, client.AuthToken)
				response, responseTime, err := concurrentClient.ExecuteCode(testdata.QuickTest)

				success := false
				if err == nil && response.Success {
					success = true
					t.Logf("Concurrent request %d succeeded in %v", index, responseTime)
				} else {
					t.Logf("Concurrent request %d failed: %v", index, err)
				}

				results <- success
			}(i)
		}

		// Collect results
		successCount := 0
		for i := 0; i < numGoroutines; i++ {
			if <-results {
				successCount++
			}
		}

		// At least half should succeed (allowing for rate limiting)
		expectedMinSuccess := numGoroutines / 2
		if successCount < expectedMinSuccess {
			t.Errorf("Expected at least %d concurrent requests to succeed, got %d",
				expectedMinSuccess, successCount)
		}

		t.Logf("Concurrent authentication test: %d/%d requests succeeded", successCount, numGoroutines)
	})

	t.Run("MixedAuthConcurrency", func(t *testing.T) {
		t.Parallel()
		numGoroutines := 6
		results := make(chan string, numGoroutines)

		// Mix of valid and invalid tokens
		for i := 0; i < numGoroutines; i++ {
			go func(index int) {
				defer func() {
					if r := recover(); r != nil {
						results <- "panic"
						return
					}
				}()

				var testClient *TestClient
				var expectedResult string

				if index%2 == 0 {
					// Valid token
					testClient = NewTestClientWithConfig(client.BaseURL, client.AuthToken)
					expectedResult = "valid"
				} else {
					// Invalid token
					testClient = NewTestClientWithConfig(client.BaseURL, CreateTempToken())
					expectedResult = "invalid"
				}

				response, responseTime, err := testClient.ExecuteCode(testdata.QuickTest)

				var actualResult string
				if err != nil {
					actualResult = "error"
				} else if response.Success {
					actualResult = "success"
				} else {
					actualResult = "failure"
				}

				t.Logf("Mixed auth request %d (%s token): %s in %v",
					index, expectedResult, actualResult, responseTime)

				results <- actualResult
			}(i)
		}

		// Collect results
		resultCounts := make(map[string]int)
		for i := 0; i < numGoroutines; i++ {
			result := <-results
			resultCounts[result]++
		}

		t.Logf("Mixed authentication results: %+v", resultCounts)

		// Should have some successes and some errors
		if resultCounts["success"] == 0 {
			t.Error("Expected at least some successful authentications")
		}
		if resultCounts["error"] == 0 {
			t.Error("Expected at least some authentication errors")
		}
	})
}

// Helper function for minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
