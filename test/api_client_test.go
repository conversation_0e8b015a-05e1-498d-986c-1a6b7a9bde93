package test

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"testing"
	"time"
)

// ExecuteRequest represents the API request structure
type ExecuteRequest struct {
	CodeBase64 string `json:"code_base64"`
}

// ExecuteResponse represents the API response structure
type ExecuteResponse struct {
	ID         string `json:"id"`
	Stdout     string `json:"stdout"`
	Stderr     string `json:"stderr"`
	Error      string `json:"error"`
	Success    bool   `json:"success"`
	ExecTimeMs int64  `json:"exec_time_ms"`
	Warning    string `json:"warning,omitempty"`

	// Output provides backward compatibility by combining stdout and stderr
	Output string `json:"-"`
}

// StatusResponse represents the status endpoint response
type StatusResponse struct {
	Workers   int    `json:"workers"`
	QueueSize int    `json:"queue_size"`
	Timeout   string `json:"timeout"`
	Timestamp string `json:"timestamp"`
}

// TestClient provides HTTP client functionality for testing
type TestClient struct {
	BaseURL   string
	AuthToken string
	Client    *http.Client
}

// NewTestClient creates a new test client with default configuration
func NewTestClient() *TestClient {
	baseURL := os.Getenv("TEST_API_URL")
	if baseURL == "" {
		baseURL = "http://localhost:8080"
	}

	authToken := os.Getenv("TEST_AUTH_TOKEN")
	if authToken == "" {
		authToken = "secret-token-123456"
	}

	return &TestClient{
		BaseURL:   baseURL,
		AuthToken: authToken,
		Client:    &http.Client{Timeout: 30 * time.Second},
	}
}

// NewTestClientWithConfig creates a test client with custom configuration
func NewTestClientWithConfig(baseURL, authToken string) *TestClient {
	return &TestClient{
		BaseURL:   baseURL,
		AuthToken: authToken,
		Client:    &http.Client{Timeout: 30 * time.Second},
	}
}

// EncodeCode encodes Python code to Base64
func (c *TestClient) EncodeCode(code string) string {
	return base64.StdEncoding.EncodeToString([]byte(code))
}

// ExecuteCode executes Python code via the API and returns the response
func (c *TestClient) ExecuteCode(code string) (*ExecuteResponse, time.Duration, error) {
	codeBase64 := c.EncodeCode(code)
	request := ExecuteRequest{CodeBase64: codeBase64}

	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, 0, fmt.Errorf("JSON marshal error: %v", err)
	}

	req, err := http.NewRequest("POST", c.BaseURL+"/execute", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, 0, fmt.Errorf("create request error: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.AuthToken)

	start := time.Now()
	resp, err := c.Client.Do(req)
	responseTime := time.Since(start)

	if err != nil {
		return nil, responseTime, fmt.Errorf("HTTP error: %v", err)
	}
	defer func() { _ = resp.Body.Close() }()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, responseTime, fmt.Errorf("read response error: %v", err)
	}

	// Only accept HTTP 200 OK - business logic errors are indicated by success: false in response body
	if resp.StatusCode != http.StatusOK {
		return nil, responseTime, fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(body))
	}

	var response ExecuteResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, responseTime, fmt.Errorf("JSON unmarshal error: %v", err)
	}

	// Fill Output field for backward compatibility
	response.Output = response.Stdout + response.Stderr

	return &response, responseTime, nil
}

// GetHealth checks the health endpoint
func (c *TestClient) GetHealth() (int, string, error) {
	resp, err := c.Client.Get(c.BaseURL + "/health")
	if err != nil {
		return 0, "", fmt.Errorf("health check error: %v", err)
	}
	defer func() { _ = resp.Body.Close() }()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return resp.StatusCode, "", fmt.Errorf("read response error: %v", err)
	}

	return resp.StatusCode, string(body), nil
}

// GetStatus checks the status endpoint
func (c *TestClient) GetStatus() (*StatusResponse, error) {
	resp, err := c.Client.Get(c.BaseURL + "/status")
	if err != nil {
		return nil, fmt.Errorf("status check error: %v", err)
	}
	defer func() { _ = resp.Body.Close() }()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response error: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(body))
	}

	var status StatusResponse
	if err := json.Unmarshal(body, &status); err != nil {
		return nil, fmt.Errorf("JSON unmarshal error: %v", err)
	}

	return &status, nil
}

// Test Helper Functions

// AssertSuccess verifies that a code execution was successful
func AssertSuccess(t *testing.T, response *ExecuteResponse, err error, responseTime time.Duration) {
	t.Helper()

	if err != nil {
		t.Fatalf("Execution failed: %v", err)
	}

	if !response.Success {
		t.Fatalf("Expected success but got failure: %s", response.Error)
	}

	if response.ID == "" {
		t.Error("Response ID should not be empty")
	}

	if response.ExecTimeMs <= 0 {
		t.Error("Execution time should be positive")
	}

	if responseTime <= 0 {
		t.Error("Response time should be positive")
	}
}

// AssertError verifies that a code execution failed as expected
func AssertError(t *testing.T, response *ExecuteResponse, err error, expectedErrorPattern string) {
	t.Helper()

	// Either HTTP error or execution error is acceptable for error tests
	if err != nil {
		// HTTP level error (e.g., authentication failure)
		if expectedErrorPattern != "" && !strings.Contains(err.Error(), expectedErrorPattern) {
			t.Errorf("Expected error pattern %q but got: %v", expectedErrorPattern, err)
		}
		return
	}

	if response.Success {
		t.Fatal("Expected execution to fail but it succeeded")
	}

	if expectedErrorPattern != "" && !strings.Contains(response.Error, expectedErrorPattern) {
		t.Errorf("Expected error pattern %q but got: %s", expectedErrorPattern, response.Error)
	}
}

// AssertOutputContains verifies that output contains expected text
func AssertOutputContains(t *testing.T, response *ExecuteResponse, expected string) {
	t.Helper()

	if !strings.Contains(response.Output, expected) {
		t.Errorf("Expected output to contain %q but got: %s", expected, response.Output)
	}
}

// AssertOutputEquals verifies that output equals expected text (trimmed)
func AssertOutputEquals(t *testing.T, response *ExecuteResponse, expected string) {
	t.Helper()

	actual := strings.TrimSpace(response.Output)
	expected = strings.TrimSpace(expected)

	if actual != expected {
		t.Errorf("Expected output %q but got %q", expected, actual)
	}
}

// AssertResponseTime verifies that response time is within acceptable bounds
func AssertResponseTime(t *testing.T, responseTime time.Duration, maxTime time.Duration) {
	t.Helper()

	if responseTime > maxTime {
		t.Errorf("Response time %v exceeded maximum %v", responseTime, maxTime)
	}

	if responseTime <= 0 {
		t.Error("Response time should be positive")
	}
}

// AssertStdoutContains verifies that stdout contains expected text
func AssertStdoutContains(t *testing.T, response *ExecuteResponse, expected string) {
	t.Helper()

	if !strings.Contains(response.Stdout, expected) {
		t.Errorf("Expected stdout to contain %q but got: %s", expected, response.Stdout)
	}
}

// AssertStderrContains verifies that stderr contains expected text
func AssertStderrContains(t *testing.T, response *ExecuteResponse, expected string) {
	t.Helper()

	if !strings.Contains(response.Stderr, expected) {
		t.Errorf("Expected stderr to contain %q but got: %s", expected, response.Stderr)
	}
}

// AssertStdoutEmpty verifies that stdout is empty
func AssertStdoutEmpty(t *testing.T, response *ExecuteResponse) {
	t.Helper()

	if strings.TrimSpace(response.Stdout) != "" {
		t.Errorf("Expected empty stdout but got: %s", response.Stdout)
	}
}

// AssertStderrEmpty verifies that stderr is empty
func AssertStderrEmpty(t *testing.T, response *ExecuteResponse) {
	t.Helper()

	if strings.TrimSpace(response.Stderr) != "" {
		t.Errorf("Expected empty stderr but got: %s", response.Stderr)
	}
}

// SkipIfServerUnavailable skips the test if the server is not available
func SkipIfServerUnavailable(t *testing.T, client *TestClient) {
	t.Helper()

	_, _, err := client.GetHealth()
	if err != nil {
		t.Skipf("Server not available at %s: %v", client.BaseURL, err)
	}
}

// CreateTempToken creates a temporary invalid token for testing
func CreateTempToken() string {
	return "temp-invalid-token-" + fmt.Sprintf("%d", time.Now().UnixNano())
}
