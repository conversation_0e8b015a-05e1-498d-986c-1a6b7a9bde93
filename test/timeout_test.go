package test

import (
	"strings"
	"testing"
	"time"
)

// TestTimeoutBehavior tests various timeout scenarios and resource cleanup
func TestTimeoutBehavior(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("InfiniteLoop", func(t *testing.T) {
		t.<PERSON>llel()

		// This should timeout after ~30s (server timeout)
		code := `
import time
count = 0
print("Starting infinite loop...")
while True:
    count += 1
    time.sleep(0.5)
    if count % 10 == 0:
        print(f"Still running... count={count}")
`
		start := time.Now()
		response, _, err := client.ExecuteCode(code)
		duration := time.Since(start)

		t.Logf("Timeout test completed in %v", duration)

		// Should not succeed due to timeout
		if err == nil && response.Success {
			t.Errorf("Expected timeout but code succeeded")
		}

		// Should timeout around 30 seconds
		if duration < 25*time.Second || duration > 35*time.Second {
			t.<PERSON><PERSON><PERSON>("Expected timeout around 30s, got %v", duration)
		}

		// Should have some output before timeout
		if response != nil && response.Stdout == "" {
			t.Error("Expected some stdout output before timeout")
		}

		if response != nil {
			t.Logf("✓ Timeout test: Duration=%v, Output=%q, Error=%q",
				duration, response.Stdout, response.Error)
		} else {
			t.Logf("✓ Timeout test: Duration=%v, No response (timeout/error)", duration)
		}
	})

	t.Run("MemoryHeavyOperation", func(t *testing.T) {
		t.Parallel()

		// Test memory-intensive operation that should be limited
		code := `
import sys
print("Starting memory test...")

# Try to allocate large amounts of memory
try:
    data = []
    for i in range(100000):
        # Each iteration adds ~1MB
        chunk = 'x' * (1024 * 1024)
        data.append(chunk)
        if i % 10 == 0:
            print(f"Allocated {i+1} MB")
            sys.stdout.flush()
except MemoryError as e:
    print(f"Memory limit reached: {e}")
except Exception as e:
    print(f"Other error: {e}")
    
print("Memory test completed")
`
		start := time.Now()
		response, _, err := client.ExecuteCode(code)
		duration := time.Since(start)

		t.Logf("Memory test completed in %v", duration)

		// This should either complete with memory error or timeout
		if err != nil {
			t.Logf("HTTP error (expected for resource limits): %v", err)
		} else if response.Success {
			t.Logf("✓ Completed successfully with output: %q", response.Stdout)
		} else {
			t.Logf("✓ Failed as expected with error: %q", response.Error)
		}

		// Should not take too long (either limited or timeout)
		if duration > 35*time.Second {
			t.Errorf("Memory test took too long: %v", duration)
		}
	})

	t.Run("ProcessSpawning", func(t *testing.T) {
		t.Parallel()

		// Test subprocess spawning limits
		code := `
import subprocess
import sys
import time

print("Testing process spawning limits...")

processes = []
try:
    for i in range(20):  # Try to spawn more processes than limit
        proc = subprocess.Popen([sys.executable, '-c', 'import time; time.sleep(10)'], 
                               stdout=subprocess.PIPE, 
                               stderr=subprocess.PIPE)
        processes.append(proc)
        print(f"Spawned process {i+1}")
        time.sleep(0.1)
        
except Exception as e:
    print(f"Process limit reached: {e}")

print(f"Total processes spawned: {len(processes)}")

# Clean up
for proc in processes:
    try:
        proc.terminate()
    except:
        pass
        
print("Process test completed")
`
		response, _, err := client.ExecuteCode(code)

		if err != nil {
			t.Logf("HTTP error (may be expected): %v", err)
		} else {
			t.Logf("Process test result: Success=%v, Output=%q, Error=%q",
				response.Success, response.Stdout, response.Error)
		}

		// Should complete within reasonable time
		// Response time check removed as variable is not captured in this test
	})
}

// TestErrorHandlingAndStderr tests stderr capture and error handling
func TestErrorHandlingAndStderr(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("PythonSyntaxError", func(t *testing.T) {
		t.Parallel()

		code := `
print("This will work")
invalid syntax here
print("This won't be reached")
`
		response, _, err := client.ExecuteCode(code)

		// Should get a response (not HTTP error)
		if err != nil {
			t.Fatalf("Unexpected HTTP error: %v", err)
		}

		// Should fail due to syntax error
		if response.Success {
			t.Error("Expected syntax error but code succeeded")
		}

		// Should have stderr content
		if response.Stderr == "" {
			t.Error("Expected stderr content for syntax error")
		}

		// Should contain syntax error information
		if !strings.Contains(response.Stderr, "SyntaxError") &&
			!strings.Contains(response.Error, "SyntaxError") {
			t.Errorf("Expected SyntaxError in stderr or error, got stderr=%q, error=%q",
				response.Stderr, response.Error)
		}

		t.Logf("✓ Syntax error test: Stderr=%q, Error=%q", response.Stderr, response.Error)
	})

	t.Run("PythonRuntimeError", func(t *testing.T) {
		t.Parallel()

		code := `
print("Before error")
x = 1 / 0  # Division by zero
print("After error")
`
		response, _, err := client.ExecuteCode(code)

		if err != nil {
			t.Fatalf("Unexpected HTTP error: %v", err)
		}

		// Should fail due to runtime error
		if response.Success {
			t.Error("Expected runtime error but code succeeded")
		}

		// Should have stdout content before error
		if !strings.Contains(response.Stdout, "Before error") {
			t.Errorf("Expected stdout content before error, got: %q", response.Stdout)
		}

		// Should have stderr content
		if response.Stderr == "" && response.Error == "" {
			t.Error("Expected stderr or error content for runtime error")
		}

		t.Logf("✓ Runtime error test: Stdout=%q, Stderr=%q, Error=%q",
			response.Stdout, response.Stderr, response.Error)
	})

	t.Run("MixedOutputStreams", func(t *testing.T) {
		t.Parallel()

		code := `
import time

print("Line 1 to stdout")
# Can't write to stderr without sys module, so just test stdout with multiple lines
time.sleep(0.1)
print("Line 2 to stdout")  
time.sleep(0.1)
print("Final stdout line")
`
		response, respTime, err := client.ExecuteCode(code)

		AssertSuccess(t, response, err, respTime)

		// Should have content in stdout
		if response.Stdout == "" {
			t.Error("Expected stdout content")
		}

		// Check for expected content
		AssertStdoutContains(t, response, "Line 1 to stdout")
		AssertStdoutContains(t, response, "Final stdout line")

		t.Logf("✓ Mixed streams test: Stdout=%q, Stderr=%q",
			response.Stdout, response.Stderr)
	})
}

// TestResourceCleanup tests that resources are properly cleaned up
func TestResourceCleanup(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("SuccessiveExecutions", func(t *testing.T) {
		t.Parallel()

		// Run multiple executions to test resource cleanup
		for i := 0; i < 5; i++ {
			code := `
import tempfile
import os

print(f"Execution {i+1}")
temp_file = tempfile.NamedTemporaryFile(delete=False)
temp_file.write(b"test data")
temp_file.close()

print(f"Created temp file: {temp_file.name}")
os.unlink(temp_file.name)
print("Cleaned up temp file")
`
			response, respTime, err := client.ExecuteCode(code)

			AssertSuccess(t, response, err, respTime)
			AssertResponseTime(t, respTime, 10*time.Second)

			if !strings.Contains(response.Stdout, "Created temp file") {
				t.Errorf("Execution %d failed to create temp file", i+1)
			}

			t.Logf("✓ Execution %d completed in %v", i+1, respTime)
		}
	})
}

// BenchmarkTimeoutHandling benchmarks timeout handling performance
func BenchmarkTimeoutHandling(b *testing.B) {
	client := NewTestClient()

	// Skip if server unavailable
	if _, _, err := client.GetHealth(); err != nil {
		b.Skipf("Server not available: %v", err)
	}

	// Quick timeout test (should complete quickly)
	code := `
import time
print("Quick operation")
time.sleep(0.1)
print("Completed")
`

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _, err := client.ExecuteCode(code)
		if err != nil {
			b.Fatal(err)
		}
	}
}
