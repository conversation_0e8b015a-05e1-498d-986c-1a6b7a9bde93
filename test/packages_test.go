package test

import (
	_ "strings"
	"testing"
	"time"

	"code-sandbox/test/testdata"
)

func TestPreinstalledPackages(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	tests := []struct {
		name             string
		code             string
		expectedInOutput []string
		maxTime          time.Duration
	}{
		{
			name:             "NumPy",
			code:             testdata.NumPyTest,
			expectedInOutput: []string{"NumPy版本:", "数组: [1 3 5 7 9]", "平均值: 5.0", "标准差:"},
			maxTime:          10 * time.Second,
		},
		{
			name:             "Pandas",
			code:             testdata.PandasTest,
			expectedInOutput: []string{"Pandas版本:", "Name", "Score", "Alice", "<PERSON>", "<PERSON>", "平均分数:"},
			maxTime:          15 * time.Second,
		},
		{
			name:             "Requests",
			code:             testdata.RequestsTest,
			expectedInOutput: []string{"Requests版本:", "Session对象创建成功", "HTTP库功能正常"},
			maxTime:          10 * time.Second,
		},
		{
			name:             "Matplotlib",
			code:             testdata.MatplotlibTest,
			expectedInOutput: []string{"Matplotlib版本:", "数据点: x=[1, 2, 3, 4, 5]", "可视化库加载成功"},
			maxTime:          15 * time.Second,
		},
		{
			name:             "ComprehensivePackages",
			code:             testdata.ComprehensivePackageTest,
			expectedInOutput: []string{"NumPy:", "Pandas:", "Requests:", "Matplotlib:", "Pillow:", "✅ 所有主要包导入成功！"},
			maxTime:          20 * time.Second,
		},
		{
			name:             "DataProcessingWorkflow",
			code:             testdata.DataProcessingWorkflow,
			expectedInOutput: []string{"原始数据:", "统计信息:", "平均值:", "最大值:", "最小值:", "按类别分组平均值:", "✅ 数据处理流程完成！"},
			maxTime:          15 * time.Second,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel() // Package tests can run in parallel

			response, responseTime, err := client.ExecuteCode(tt.code)
			AssertSuccess(t, response, err, responseTime)
			AssertResponseTime(t, responseTime, tt.maxTime)

			// Check that all expected strings are in output
			for _, expected := range tt.expectedInOutput {
				AssertOutputContains(t, response, expected)
			}

			t.Logf("%s completed in %v (execution: %dms)",
				tt.name, responseTime, response.ExecTimeMs)
		})
	}
}

func TestIndividualPackages(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("NumPyAdvanced", func(t *testing.T) {
		t.Parallel()
		code := `
import numpy as np

# 创建不同类型的数组
arr1d = np.array([1, 2, 3, 4, 5])
arr2d = np.array([[1, 2, 3], [4, 5, 6]])
arr_zeros = np.zeros(5)
arr_ones = np.ones((2, 3))
arr_range = np.arange(0, 10, 2)

print(f"1D数组: {arr1d}")
print(f"2D数组:\n{arr2d}")
print(f"零数组: {arr_zeros}")
print(f"1数组:\n{arr_ones}")
print(f"范围数组: {arr_range}")

# 数学运算
print(f"数组求和: {np.sum(arr1d)}")
print(f"数组平均值: {np.mean(arr1d)}")
print(f"数组标准差: {np.std(arr1d):.2f}")
print(f"数组最大值: {np.max(arr1d)}")
print(f"数组最小值: {np.min(arr1d)}")

# 矩阵运算
matrix_a = np.array([[1, 2], [3, 4]])
matrix_b = np.array([[5, 6], [7, 8]])
print(f"矩阵乘法:\n{np.dot(matrix_a, matrix_b)}")
`
		response, responseTime, err := client.ExecuteCode(code)
		AssertSuccess(t, response, err, responseTime)
		AssertOutputContains(t, response, "1D数组: [1 2 3 4 5]")
		AssertOutputContains(t, response, "数组求和: 15")
		AssertOutputContains(t, response, "数组平均值: 3.0")
	})

	t.Run("PandasAdvanced", func(t *testing.T) {
		t.Parallel()
		code := `
import pandas as pd
import numpy as np

# 创建DataFrame
data = {
    'Name': ['张三', '李四', '王五', '赵六'],
    'Age': [25, 30, 35, 28],
    'City': ['北京', '上海', '广州', '深圳'],
    'Salary': [50000, 60000, 70000, 55000]
}

df = pd.DataFrame(data)
print("原始数据:")
print(df)

# 基本统计
print(f"\n年龄统计:")
print(df['Age'].describe())

print(f"\n平均工资: {df['Salary'].mean()}")
print(f"最高工资: {df['Salary'].max()}")
print(f"最低工资: {df['Salary'].min()}")

# 筛选数据
high_salary = df[df['Salary'] > 55000]
print(f"\n高工资员工:")
print(high_salary[['Name', 'Salary']])

# 按城市分组
city_avg = df.groupby('City')['Salary'].mean()
print(f"\n各城市平均工资:")
print(city_avg)
`
		response, responseTime, err := client.ExecuteCode(code)
		AssertSuccess(t, response, err, responseTime)
		AssertOutputContains(t, response, "张三")
		AssertOutputContains(t, response, "平均工资:")
		AssertOutputContains(t, response, "高工资员工:")
	})

	t.Run("RequestsAdvanced", func(t *testing.T) {
		if testing.Short() {
			t.Skip("Skipping network test in short mode")
		}
		t.Parallel()
		code := `
import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

# 测试会话配置
session = requests.Session()
print(f"Requests版本: {requests.__version__}")

# 配置重试策略
retry_strategy = Retry(
    total=3,
    status_forcelist=[429, 500, 502, 503, 504],
    method_whitelist=["HEAD", "GET", "OPTIONS"]
)

adapter = HTTPAdapter(max_retries=retry_strategy)
session.mount("http://", adapter)
session.mount("https://", adapter)

print("会话配置完成")
print("HTTP适配器已设置")
print("重试策略已配置")

# 测试请求头
headers = {
    'User-Agent': 'Python-Requests-Test/1.0',
    'Accept': 'application/json'
}

print(f"自定义请求头: {headers}")
print("✅ Requests库配置完成")
`
		response, responseTime, err := client.ExecuteCode(code)
		AssertSuccess(t, response, err, responseTime)
		AssertOutputContains(t, response, "Requests版本:")
		AssertOutputContains(t, response, "会话配置完成")
		AssertOutputContains(t, response, "✅ Requests库配置完成")
	})

	t.Run("MatplotlibAdvanced", func(t *testing.T) {
		t.Parallel()
		code := `
import matplotlib
import matplotlib.pyplot as plt
import numpy as np

print(f"Matplotlib版本: {matplotlib.__version__}")

# 生成数据
x = np.linspace(0, 2*np.pi, 100)
y_sin = np.sin(x)
y_cos = np.cos(x)

print(f"数据点数量: {len(x)}")
print(f"X范围: [{x[0]:.2f}, {x[-1]:.2f}]")
print(f"Sin Y范围: [{np.min(y_sin):.2f}, {np.max(y_sin):.2f}]")
print(f"Cos Y范围: [{np.min(y_cos):.2f}, {np.max(y_cos):.2f}]")

# 创建图形对象（不实际显示）
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8))

print(f"图形对象创建成功")
print(f"子图数量: 2")
print(f"图形大小: 10x8")

# 设置样式
plt.style.available
print(f"可用样式数量: {len(plt.style.available)}")

print("✅ Matplotlib配置完成")
`
		response, responseTime, err := client.ExecuteCode(code)
		AssertSuccess(t, response, err, responseTime)
		AssertOutputContains(t, response, "Matplotlib版本:")
		AssertOutputContains(t, response, "数据点数量: 100")
		AssertOutputContains(t, response, "图形对象创建成功")
		AssertOutputContains(t, response, "✅ Matplotlib配置完成")
	})
}

func TestPackageVersions(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("VersionCheck", func(t *testing.T) {
		t.Parallel()
		code := `
import sys
import numpy as np
import pandas as pd
import requests
import matplotlib

print("=== Python环境信息 ===")
print(f"Python版本: {sys.version}")
print(f"平台: {sys.platform}")

print("\n=== 包版本信息 ===")
print(f"NumPy: {np.__version__}")
print(f"Pandas: {pd.__version__}")
print(f"Requests: {requests.__version__}")
print(f"Matplotlib: {matplotlib.__version__}")

# 验证版本符合要求（根据项目文档）
import pkg_resources

packages = [
    ('numpy', '2.3.2'),
    ('pandas', '2.3.2'),
    ('requests', '2.32.5'),
    ('matplotlib', '3.10.6')
]

print("\n=== 版本兼容性检查 ===")
for package, min_version in packages:
    try:
        installed = pkg_resources.get_distribution(package).version
        print(f"{package}: {installed} (最小要求: {min_version})")
    except Exception as e:
        print(f"{package}: 检查失败 - {e}")

print("✅ 版本检查完成")
`
		response, responseTime, err := client.ExecuteCode(code)
		AssertSuccess(t, response, err, responseTime)
		AssertOutputContains(t, response, "Python版本:")
		AssertOutputContains(t, response, "NumPy:")
		AssertOutputContains(t, response, "Pandas:")
		AssertOutputContains(t, response, "Requests:")
		AssertOutputContains(t, response, "Matplotlib:")
		AssertOutputContains(t, response, "✅ 版本检查完成")
	})
}

func TestOptionalPackages(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("PillowTest", func(t *testing.T) {
		t.Parallel()
		code := `
try:
    from PIL import Image, ImageDraw, ImageFont
    import io
    
    print("Pillow (PIL) 导入成功")
    
    # 创建一个简单的图像
    img = Image.new('RGB', (100, 100), color='red')
    print(f"图像模式: {img.mode}")
    print(f"图像尺寸: {img.size}")
    
    # 转换格式
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    img_size = len(img_bytes.getvalue())
    print(f"PNG格式大小: {img_size} bytes")
    
    print("✅ Pillow功能正常")
    
except ImportError as e:
    print(f"Pillow未安装: {e}")
except Exception as e:
    print(f"Pillow测试错误: {e}")
`
		response, responseTime, err := client.ExecuteCode(code)
		AssertSuccess(t, response, err, responseTime)
		// Pillow should be installed according to project docs
		AssertOutputContains(t, response, "Pillow (PIL) 导入成功")
		AssertOutputContains(t, response, "✅ Pillow功能正常")
	})

	t.Run("YAMLTest", func(t *testing.T) {
		t.Parallel()
		code := `
try:
    import yaml
    
    print("PyYAML 导入成功")
    
    # 创建测试数据
    data = {
        'name': '测试配置',
        'version': '1.0',
        'settings': {
            'debug': True,
            'timeout': 30,
            'features': ['auth', 'logging', 'monitoring']
        }
    }
    
    # 转换为YAML
    yaml_str = yaml.dump(data, default_flow_style=False, allow_unicode=True)
    print("YAML格式:")
    print(yaml_str)
    
    # 解析YAML
    parsed_data = yaml.safe_load(yaml_str)
    print(f"解析结果 - 名称: {parsed_data['name']}")
    print(f"解析结果 - 版本: {parsed_data['version']}")
    
    print("✅ PyYAML功能正常")
    
except ImportError as e:
    print(f"PyYAML未安装: {e}")
except Exception as e:
    print(f"PyYAML测试错误: {e}")
`
		response, responseTime, err := client.ExecuteCode(code)
		AssertSuccess(t, response, err, responseTime)
		AssertOutputContains(t, response, "PyYAML 导入成功")
		AssertOutputContains(t, response, "✅ PyYAML功能正常")
	})

	t.Run("BeautifulSoupTest", func(t *testing.T) {
		t.Parallel()
		code := `
try:
    from bs4 import BeautifulSoup
    
    print("BeautifulSoup4 导入成功")
    
    # 创建测试HTML
    html = """
    <html>
        <head><title>测试页面</title></head>
        <body>
            <h1>标题</h1>
            <p class="content">段落内容</p>
            <ul>
                <li>项目1</li>
                <li>项目2</li>
            </ul>
        </body>
    </html>
    """
    
    # 解析HTML
    soup = BeautifulSoup(html, 'html.parser')
    
    title = soup.find('title').text
    h1 = soup.find('h1').text
    p_content = soup.find('p', class_='content').text
    li_items = [li.text for li in soup.find_all('li')]
    
    print(f"标题: {title}")
    print(f"H1: {h1}")
    print(f"段落: {p_content}")
    print(f"列表项: {li_items}")
    
    print("✅ BeautifulSoup4功能正常")
    
except ImportError as e:
    print(f"BeautifulSoup4未安装: {e}")
except Exception as e:
    print(f"BeautifulSoup4测试错误: {e}")
`
		response, responseTime, err := client.ExecuteCode(code)
		AssertSuccess(t, response, err, responseTime)
		AssertOutputContains(t, response, "BeautifulSoup4 导入成功")
		AssertOutputContains(t, response, "标题: 测试页面")
		AssertOutputContains(t, response, "✅ BeautifulSoup4功能正常")
	})

	t.Run("UjsonTest", func(t *testing.T) {
		t.Parallel()
		code := `
try:
    import ujson
    import json
    import time
    
    print("ujson 导入成功")
    
    # 测试数据
    test_data = {
        'name': '性能测试',
        'numbers': list(range(1000)),
        'nested': {
            'level1': {
                'level2': {
                    'data': 'deep nested value'
                }
            }
        }
    }
    
    # ujson编码
    start_time = time.time()
    ujson_str = ujson.dumps(test_data)
    ujson_time = time.time() - start_time
    
    # 标准json编码
    start_time = time.time()
    json_str = json.dumps(test_data)
    json_time = time.time() - start_time
    
    print(f"ujson编码时间: {ujson_time:.6f}s")
    print(f"标准json编码时间: {json_time:.6f}s")
    print(f"数据长度: {len(ujson_str)}")
    
    # 解码测试
    decoded = ujson.loads(ujson_str)
    print(f"解码验证 - 名称: {decoded['name']}")
    print(f"解码验证 - 数组长度: {len(decoded['numbers'])}")
    
    print("✅ ujson功能正常")
    
except ImportError as e:
    print(f"ujson未安装: {e}")
except Exception as e:
    print(f"ujson测试错误: {e}")
`
		response, responseTime, err := client.ExecuteCode(code)
		AssertSuccess(t, response, err, responseTime)
		AssertOutputContains(t, response, "ujson 导入成功")
		AssertOutputContains(t, response, "✅ ujson功能正常")
	})
}

// Benchmark package performance
func BenchmarkPackagePerformance(b *testing.B) {
	client := NewTestClient()

	// Skip if server unavailable
	if _, _, err := client.GetHealth(); err != nil {
		b.Skipf("Server not available: %v", err)
	}

	b.Run("NumPyOperations", func(b *testing.B) {
		code := `
import numpy as np
arr = np.random.random((1000, 1000))
result = np.sum(arr)
print(f"Sum: {result:.2f}")
`
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, _, err := client.ExecuteCode(code)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("PandasDataFrame", func(b *testing.B) {
		code := `
import pandas as pd
import numpy as np
df = pd.DataFrame(np.random.randn(10000, 4), columns=['A', 'B', 'C', 'D'])
result = df.mean()
print(f"Means: {result.values}")
`
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, _, err := client.ExecuteCode(code)
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}
