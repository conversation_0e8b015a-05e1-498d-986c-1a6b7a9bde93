package test

import (
	"testing"
	"time"

	"code-sandbox/test/testdata"
)

func TestErrorHandling(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	tests := []struct {
		name              string
		code              string
		expectedErrorType string
		maxTime           time.Duration
	}{
		{
			name:              "SyntaxError",
			code:              testdata.SyntaxError,
			expectedErrorType: "SyntaxError",
			maxTime:           10 * time.Second,
		},
		{
			name:              "RuntimeError",
			code:              testdata.RuntimeError,
			expectedErrorType: "ZeroDivisionError",
			maxTime:           10 * time.Second,
		},
		{
			name:              "ImportError",
			code:              testdata.ImportError,
			expectedErrorType: "ModuleNotFoundError",
			maxTime:           10 * time.Second,
		},
		{
			name:              "NameError",
			code:              testdata.NameError,
			expectedErrorType: "NameError",
			maxTime:           10 * time.Second,
		},
		{
			name:              "TypeError",
			code:              testdata.TypeError,
			expectedErrorType: "TypeError",
			maxTime:           10 * time.Second,
		},
		{
			name:              "IndentationError",
			code:              testdata.IndentationError,
			expectedErrorType: "IndentationError",
			maxTime:           10 * time.Second,
		},
		{
			name:              "AttributeError",
			code:              testdata.AttributeError,
			expectedErrorType: "AttributeError",
			maxTime:           10 * time.Second,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel() // Error tests can run in parallel

			response, responseTime, err := client.ExecuteCode(tt.code)

			// For error tests, we expect either:
			// 1. HTTP error (server couldn't process the request)
			// 2. Successful response but with success=false (Python execution failed)
			if err != nil {
				// HTTP level error - this might be acceptable for some error types
				t.Logf("HTTP error (expected for error test): %v", err)
				AssertResponseTime(t, responseTime, tt.maxTime)
				return
			}

			// Python execution should have failed
			if response.Success {
				t.Fatalf("Expected Python execution to fail but it succeeded. Output: %s", response.Output)
			}

			// Verify the error type is what we expect
			if tt.expectedErrorType != "" {
				AssertError(t, response, err, tt.expectedErrorType)
			}

			// Verify response time is reasonable
			AssertResponseTime(t, responseTime, tt.maxTime)

			t.Logf("%s correctly failed in %v with error: %s",
				tt.name, responseTime, response.Error)
		})
	}
}

func TestSpecificErrorTypes(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("RecursionError", func(t *testing.T) {
		t.Parallel()
		code := `
def infinite_recursion():
    return infinite_recursion()

infinite_recursion()
`
		response, responseTime, err := client.ExecuteCode(code)
		if err == nil {
			AssertError(t, response, err, "RecursionError")
		}
		AssertResponseTime(t, responseTime, 15*time.Second)
	})

	t.Run("IndexError", func(t *testing.T) {
		t.Parallel()
		code := `
arr = [1, 2, 3]
print(arr[10])  # Index out of range
`
		response, responseTime, err := client.ExecuteCode(code)
		if err == nil {
			AssertError(t, response, err, "IndexError")
		}
		AssertResponseTime(t, responseTime, 10*time.Second)
	})

	t.Run("KeyError", func(t *testing.T) {
		t.Parallel()
		code := `
data = {"name": "test"}
print(data["nonexistent_key"])
`
		response, responseTime, err := client.ExecuteCode(code)
		if err == nil {
			AssertError(t, response, err, "KeyError")
		}
		AssertResponseTime(t, responseTime, 10*time.Second)
	})

	t.Run("ValueError", func(t *testing.T) {
		t.Parallel()
		code := `
num = int("not_a_number")
print(num)
`
		response, responseTime, err := client.ExecuteCode(code)
		if err == nil {
			AssertError(t, response, err, "ValueError")
		}
		AssertResponseTime(t, responseTime, 10*time.Second)
	})

	t.Run("FileNotFoundError", func(t *testing.T) {
		t.Parallel()
		code := `
with open("nonexistent_file.txt", "r") as f:
    content = f.read()
    print(content)
`
		response, responseTime, err := client.ExecuteCode(code)
		if err == nil {
			AssertError(t, response, err, "FileNotFoundError")
		}
		AssertResponseTime(t, responseTime, 10*time.Second)
	})

	t.Run("MemoryError", func(t *testing.T) {
		if testing.Short() {
			t.Skip("Skipping memory error test in short mode")
		}
		t.Parallel()
		code := `
# Try to allocate a huge amount of memory
try:
    huge_list = [0] * (10**9)  # 1 billion integers
    print("Memory allocation succeeded")
except MemoryError:
    print("MemoryError caught as expected")
except Exception as e:
    print(f"Other error: {type(e).__name__}: {e}")
`
		response, responseTime, err := client.ExecuteCode(code)
		AssertSuccess(t, response, err, responseTime)
		// This test should succeed and handle the memory error gracefully
		AssertResponseTime(t, responseTime, 20*time.Second)
	})
}

func TestErrorRecovery(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("ErrorThenSuccess", func(t *testing.T) {
		// First execute code that will fail
		errorCode := testdata.SyntaxError
		response1, responseTime1, err1 := client.ExecuteCode(errorCode)

		if err1 == nil && response1.Success {
			t.Fatal("Expected first execution to fail")
		}

		t.Logf("Error execution completed in %v", responseTime1)

		// Then execute code that should succeed
		successCode := testdata.SimplePrint
		response2, responseTime2, err2 := client.ExecuteCode(successCode)

		AssertSuccess(t, response2, err2, responseTime2)
		AssertOutputContains(t, response2, "Hello from Docker!")

		t.Logf("Success execution completed in %v after error", responseTime2)
	})

	t.Run("MultipleErrorsInSequence", func(t *testing.T) {
		errorCodes := []struct {
			name string
			code string
		}{
			{"SyntaxError", testdata.SyntaxError},
			{"RuntimeError", testdata.RuntimeError},
			{"NameError", testdata.NameError},
		}

		for i, errorTest := range errorCodes {
			t.Run(errorTest.name, func(t *testing.T) {
				response, responseTime, err := client.ExecuteCode(errorTest.code)

				// Should fail
				if err == nil && response.Success {
					t.Errorf("Expected error test %d (%s) to fail", i+1, errorTest.name)
				}

				AssertResponseTime(t, responseTime, 10*time.Second)
				t.Logf("Error test %d (%s) completed in %v", i+1, errorTest.name, responseTime)
			})
		}

		// Final success test
		t.Run("FinalSuccess", func(t *testing.T) {
			response, responseTime, err := client.ExecuteCode(testdata.SimplePrint)
			AssertSuccess(t, response, err, responseTime)
			AssertOutputContains(t, response, "Hello from Docker!")
			t.Logf("Final success test completed in %v", responseTime)
		})
	})
}

func TestErrorMessages(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("DetailedErrorInfo", func(t *testing.T) {
		t.Parallel()
		code := `
def problematic_function():
    x = 1
    y = 0
    result = x / y  # This will cause ZeroDivisionError
    return result

def caller():
    return problematic_function()

# Call the function to trigger error
caller()
`
		response, responseTime, err := client.ExecuteCode(code)

		if err == nil {
			// Python execution should fail
			if response.Success {
				t.Fatal("Expected execution to fail with ZeroDivisionError")
			}

			// Check that error message contains useful information
			if response.Error == "" {
				t.Error("Expected non-empty error message")
			}

			// Check for function names in traceback
			AssertError(t, response, err, "ZeroDivisionError")

			t.Logf("Error message: %s", response.Error)
		}

		AssertResponseTime(t, responseTime, 10*time.Second)
	})

	t.Run("LineNumberInError", func(t *testing.T) {
		t.Parallel()
		code := `
# Line 1
print("Starting execution")  # Line 2
# Line 3
x = undefined_variable  # Line 4 - This should cause NameError
print("Should not reach here")  # Line 5
`
		response, responseTime, err := client.ExecuteCode(code)

		if err == nil {
			AssertError(t, response, err, "NameError")

			// Error message should contain line number information
			if response.Error != "" {
				t.Logf("Error with line info: %s", response.Error)
			}
		}

		AssertResponseTime(t, responseTime, 10*time.Second)
	})
}

func TestLargeErrorOutput(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("LongStackTrace", func(t *testing.T) {
		if testing.Short() {
			t.Skip("Skipping long stack trace test in short mode")
		}
		t.Parallel()

		code := `
def func_level_10():
    raise Exception("Deep error at level 10")

def func_level_9():
    return func_level_10()

def func_level_8():
    return func_level_9()

def func_level_7():
    return func_level_8()

def func_level_6():
    return func_level_7()

def func_level_5():
    return func_level_6()

def func_level_4():
    return func_level_5()

def func_level_3():
    return func_level_4()

def func_level_2():
    return func_level_3()

def func_level_1():
    return func_level_2()

# This will create a deep stack trace
func_level_1()
`
		response, responseTime, err := client.ExecuteCode(code)

		if err == nil {
			AssertError(t, response, err, "Exception")

			// Check that we get a reasonable error message (not truncated too severely)
			if len(response.Error) < 50 {
				t.Error("Expected longer error message for deep stack trace")
			}

			t.Logf("Deep stack trace length: %d characters", len(response.Error))
		}

		AssertResponseTime(t, responseTime, 15*time.Second)
	})
}

func TestTimeoutHandling(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("LongRunningCode", func(t *testing.T) {
		if testing.Short() {
			t.Skip("Skipping timeout test in short mode")
		}

		// This test might take the full timeout period
		code := testdata.TimeoutTest // Sleeps for 35 seconds, should timeout at 30s

		response, responseTime, err := client.ExecuteCode(code)

		// This should either timeout (HTTP error) or be killed by the server
		if err != nil {
			// HTTP timeout or server error - acceptable
			t.Logf("Expected timeout error: %v", err)
		} else if !response.Success {
			// Server killed the execution - also acceptable
			t.Logf("Server terminated long-running code: %s", response.Error)
		} else {
			// If it somehow succeeded, that's unexpected
			t.Error("Expected long-running code to timeout or be terminated")
		}

		// Response time should be around the timeout period (30s) or less
		maxExpectedTime := 35 * time.Second
		if responseTime > maxExpectedTime {
			t.Errorf("Response time %v exceeded maximum expected timeout %v", responseTime, maxExpectedTime)
		}

		t.Logf("Timeout test completed in %v", responseTime)
	})
}

func TestErrorAndSuccessAlternating(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	// Test that the system can handle alternating error and success cases
	testCases := []struct {
		name          string
		code          string
		shouldSucceed bool
	}{
		{"Success1", testdata.SimplePrint, true},
		{"Error1", testdata.SyntaxError, false},
		{"Success2", testdata.MathCalculation, true},
		{"Error2", testdata.RuntimeError, false},
		{"Success3", testdata.StringOperations, true},
		{"Error3", testdata.NameError, false},
		{"FinalSuccess", testdata.ListProcessing, true},
	}

	for i, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			response, responseTime, err := client.ExecuteCode(tc.code)

			if tc.shouldSucceed {
				AssertSuccess(t, response, err, responseTime)
				t.Logf("Success case %d completed in %v", i+1, responseTime)
			} else {
				if err == nil && response.Success {
					t.Errorf("Expected case %d (%s) to fail but it succeeded", i+1, tc.name)
				}
				t.Logf("Error case %d completed in %v", i+1, responseTime)
			}

			AssertResponseTime(t, responseTime, 15*time.Second)
		})
	}
}
