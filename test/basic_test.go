package test

import (
	"testing"
	"time"

	"code-sandbox/test/testdata"
)

func TestBasicFunctionality(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	tests := []struct {
		name           string
		code           string
		expectedOutput string
		maxTime        time.Duration
	}{
		{
			name:           "SimplePrint",
			code:           testdata.SimplePrint,
			expectedOutput: "Hello from Docker!",
			maxTime:        5 * time.Second,
		},
		{
			name:           "MathCalculation",
			code:           testdata.MathCalculation,
			expectedOutput: "计算结果: 68",
			maxTime:        5 * time.Second,
		},
		{
			name:           "StringOperations",
			code:           testdata.StringOperations,
			expectedOutput: "原文: Go Code Executors",
			maxTime:        5 * time.Second,
		},
		{
			name:           "ListProcessing",
			code:           testdata.ListProcessing,
			expectedOutput: "求和: 30",
			maxTime:        5 * time.Second,
		},
		{
			name:           "DictionaryOperations",
			code:           testdata.DictionaryOperations,
			expectedOutput: "名称: Docker",
			maxTime:        5 * time.Second,
		},
		{
			name:           "SimpleStringConcat",
			code:           testdata.SimpleStringConcat,
			expectedOutput: "helloworld",
			maxTime:        3 * time.Second,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel() // These tests can run in parallel

			response, responseTime, err := client.ExecuteCode(tt.code)

			// Assert successful execution
			AssertSuccess(t, response, err, responseTime)

			// Assert response time is reasonable
			AssertResponseTime(t, responseTime, tt.maxTime)

			// Assert output contains expected content
			AssertOutputContains(t, response, tt.expectedOutput)

			// Additional assertions for specific tests
			switch tt.name {
			case "MathCalculation":
				AssertOutputContains(t, response, "类型: <class 'int'>")
			case "StringOperations":
				AssertOutputContains(t, response, "大写: GO CODE EXECUTORS")
				AssertOutputContains(t, response, "长度: 17")
			case "ListProcessing":
				AssertOutputContains(t, response, "列表: [2, 4, 6, 8, 10]")
				AssertOutputContains(t, response, "最大值: 10")
			case "DictionaryOperations":
				AssertOutputContains(t, response, "版本: latest")
				AssertOutputContains(t, response, "键数量: 3")
			case "SimpleStringConcat":
				AssertOutputContains(t, response, "String concatenation test passed")
			}

			t.Logf("%s completed in %v (execution: %dms)",
				tt.name, responseTime, response.ExecTimeMs)
		})
	}
}

func TestBasicPythonFeatures(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	t.Run("LoopsAndConditionals", func(t *testing.T) {
		t.Parallel()
		code := `
for i in range(5):
    if i % 2 == 0:
        print(f"偶数: {i}")
    else:
        print(f"奇数: {i}")
        
result = sum(x for x in range(10) if x % 2 == 0)
print(f"偶数和: {result}")
`
		response, responseTime, err := client.ExecuteCode(code)
		AssertSuccess(t, response, err, responseTime)
		AssertOutputContains(t, response, "偶数: 0")
		AssertOutputContains(t, response, "奇数: 1")
		AssertOutputContains(t, response, "偶数和: 20")
	})

	t.Run("FunctionDefinition", func(t *testing.T) {
		t.Parallel()
		code := `
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

def factorial(n):
    if n <= 1:
        return 1
    return n * factorial(n-1)

print(f"斐波那契数列第8项: {fibonacci(8)}")
print(f"5的阶乘: {factorial(5)}")
`
		response, responseTime, err := client.ExecuteCode(code)
		AssertSuccess(t, response, err, responseTime)
		AssertOutputContains(t, response, "斐波那契数列第8项: 21")
		AssertOutputContains(t, response, "5的阶乘: 120")
	})

	t.Run("ClassAndObject", func(t *testing.T) {
		t.Parallel()
		code := `
class Calculator:
    def __init__(self, name):
        self.name = name
    
    def add(self, a, b):
        return a + b
    
    def multiply(self, a, b):
        return a * b
    
    def info(self):
        return f"计算器: {self.name}"

calc = Calculator("测试计算器")
print(calc.info())
print(f"加法: 3 + 5 = {calc.add(3, 5)}")
print(f"乘法: 4 * 6 = {calc.multiply(4, 6)}")
`
		response, responseTime, err := client.ExecuteCode(code)
		AssertSuccess(t, response, err, responseTime)
		AssertOutputContains(t, response, "计算器: 测试计算器")
		AssertOutputContains(t, response, "加法: 3 + 5 = 8")
		AssertOutputContains(t, response, "乘法: 4 * 6 = 24")
	})

	t.Run("ExceptionHandling", func(t *testing.T) {
		t.Parallel()
		code := `
def safe_divide(a, b):
    try:
        result = a / b
        return f"结果: {result}"
    except ZeroDivisionError:
        return "错误: 除零错误"
    except Exception as e:
        return f"未知错误: {e}"
    finally:
        print("除法操作完成")

print(safe_divide(10, 2))
print(safe_divide(10, 0))
print(safe_divide("a", 2))  # This will cause TypeError
`
		response, responseTime, err := client.ExecuteCode(code)
		AssertSuccess(t, response, err, responseTime)
		AssertOutputContains(t, response, "结果: 5.0")
		AssertOutputContains(t, response, "错误: 除零错误")
		AssertOutputContains(t, response, "除法操作完成")
	})

	t.Run("ListComprehensions", func(t *testing.T) {
		t.Parallel()
		code := `
# 基本列表推导式
squares = [x**2 for x in range(10)]
print(f"平方数: {squares}")

# 带条件的列表推导式
evens = [x for x in range(20) if x % 2 == 0]
print(f"偶数: {evens}")

# 字典推导式
square_dict = {x: x**2 for x in range(5)}
print(f"平方字典: {square_dict}")

# 嵌套列表推导式
matrix = [[j for j in range(3)] for i in range(3)]
print(f"矩阵: {matrix}")
`
		response, responseTime, err := client.ExecuteCode(code)
		AssertSuccess(t, response, err, responseTime)
		AssertOutputContains(t, response, "平方数: [0, 1, 4, 9, 16")
		AssertOutputContains(t, response, "偶数: [0, 2, 4, 6")
		AssertOutputContains(t, response, "平方字典: {0: 0, 1: 1, 2: 4")
	})
}

func TestBuiltinFunctions(t *testing.T) {
	client := NewTestClient()
	SkipIfServerUnavailable(t, client)

	tests := []struct {
		name string
		code string
	}{
		{
			name: "MathFunctions",
			code: `
import math

numbers = [1, 4, 9, 16, 25]
print(f"数字列表: {numbers}")
print(f"最小值: {min(numbers)}")
print(f"最大值: {max(numbers)}")
print(f"求和: {sum(numbers)}")
print(f"长度: {len(numbers)}")
print(f"排序: {sorted(numbers, reverse=True)}")
print(f"平方根 of 16: {math.sqrt(16)}")
print(f"2的3次方: {math.pow(2, 3)}")
`,
		},
		{
			name: "StringFunctions",
			code: `
text = "  Hello World Python  "
print(f"原始: '{text}'")
print(f"去空格: '{text.strip()}'")
print(f"大写: '{text.upper()}'")
print(f"小写: '{text.lower()}'")
print(f"替换: '{text.replace('World', 'Universe')}'")
print(f"分割: {text.strip().split(' ')}")
print(f"是否包含Python: {'Python' in text}")
`,
		},
		{
			name: "TypeConversions",
			code: `
# 类型转换测试
print(f"字符串转整数: {int('42')}")
print(f"整数转字符串: '{str(42)}'")
print(f"字符串转浮点: {float('3.14')}")
print(f"布尔转整数: {int(True)}, {int(False)}")
print(f"列表转集合: {set([1, 2, 2, 3, 3])}")
print(f"字符串转列表: {list('hello')}")
print(f"元组转列表: {list((1, 2, 3))}")
`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			response, responseTime, err := client.ExecuteCode(tt.code)
			AssertSuccess(t, response, err, responseTime)
			AssertResponseTime(t, responseTime, 10*time.Second)

			// Verify output is not empty
			if response.Output == "" {
				t.Error("Expected non-empty output")
			}
		})
	}
}

// Benchmark basic operations
func BenchmarkBasicOperations(b *testing.B) {
	client := NewTestClient()

	// Skip if server unavailable
	if _, _, err := client.GetHealth(); err != nil {
		b.Skipf("Server not available: %v", err)
	}

	b.Run("SimplePrint", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_, _, err := client.ExecuteCode(testdata.SimplePrint)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("MathCalculation", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_, _, err := client.ExecuteCode(testdata.MathCalculation)
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}
