package test

import (
	"context"
	"fmt"
	"log"
	"testing"
	"time"

	"code-sandbox/internal/config"
	"code-sandbox/internal/executor"
)

func TestExecutorTimeout(t *testing.T) {
	// Test the timeout and resource cleanup improvements
	log.Println("Testing executor timeout and resource management...")

	cfg := &config.Config{
		ExecTimeout:     2 * time.Second,
		MemoryLimitMB:   256,
		MaxCodeSize:     1024 * 1024,
		FileSizeLimitKB: 1024,
		MaxProcesses:    5,
		MaxOpenFiles:    50,
		MaxStackSizeKB:  1024,
	}

	exec := executor.NewExecutor(cfg)

	// Test 1: Normal execution
	fmt.Println("=== Test 1: Normal Python execution ===")
	job1 := executor.Job{
		ID:   "test-1",
		Code: "print('Hello World')\nprint('This should work')",
	}

	result1 := exec.Execute(context.Background(), job1)
	fmt.Printf("Result 1: Success=%v, Stdout=%q, Stderr=%q, Error=%q\n",
		result1.Success, result1.Stdout, result1.Stderr, result1.Error)

	// Test 2: Timeout scenario - infinite loop
	fmt.Println("\n=== Test 2: Timeout with infinite loop ===")
	job2 := executor.Job{
		ID:   "test-2",
		Code: "import time\nwhile True:\n    time.sleep(0.1)\n    print('looping...')",
	}

	start := time.Now()
	result2 := exec.Execute(context.Background(), job2)
	duration := time.Since(start)

	fmt.Printf("Result 2: Success=%v, Error=%q, Duration=%v\n",
		result2.Success, result2.Error, duration)
	fmt.Printf("Timeout occurred: %v, Expected around 2s, Got: %v\n",
		duration > time.Second && duration < 3*time.Second, duration)

	// Test 3: Context cancellation
	fmt.Println("\n=== Test 3: Context cancellation ===")
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer cancel()

	job3 := executor.Job{
		ID:   "test-3",
		Code: "import time\ntime.sleep(5)\nprint('Should not reach here')",
	}

	start = time.Now()
	result3 := exec.Execute(ctx, job3)
	duration = time.Since(start)

	fmt.Printf("Result 3: Success=%v, Error=%q, Duration=%v\n",
		result3.Success, result3.Error, duration)
	fmt.Printf("Context cancellation worked: %v, Expected around 1s, Got: %v\n",
		duration > 500*time.Millisecond && duration < 2*time.Second, duration)

	fmt.Println("\n✅ Timeout and resource management test completed!")
}
