package test

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"code-sandbox/internal/config"
	"code-sandbox/internal/executor"
)

// TestASTValidatorBasicFunctionality tests basic AST validator functionality
func TestASTValidatorBasicFunctionality(t *testing.T) {
	cfg := createTestSecurityConfig()
	validator := executor.NewCodeValidator(cfg)

	tests := []struct {
		name           string
		code           string
		expectSafe     bool
		expectedRisk   executor.SecurityRisk
		expectedTypes  []string
		description    string
	}{
		{
			name:           "Safe Code",
			code:           "print('Hello, World!')",
			expectSafe:     true,
			expectedRisk:   executor.RiskSafe,
			expectedTypes:  []string{},
			description:    "Simple print statement should be safe",
		},
		{
			name:           "Blocked Import - os",
			code:           "import os\nos.system('echo test')",
			expectSafe:     false,
			expectedRisk:   executor.RiskCritical,
			expectedTypes:  []string{"blocked_import"},
			description:    "OS module should be blocked",
		},
		{
			name:           "Restricted Import - numpy",
			code:           "import numpy as np\narray = np.array([1, 2, 3])",
			expectSafe:     true,
			expectedRisk:   executor.RiskLow,
			expectedTypes:  []string{"restricted_import"},
			description:    "NumPy should be restricted but allowed",
		},
		{
			name:           "Code Injection - eval",
			code:           "eval('print(\"injected code\")')",
			expectSafe:     false,
			expectedRisk:   executor.RiskCritical,
			expectedTypes:  []string{"code_injection"},
			description:    "eval() should be blocked",
		},
		{
			name:           "Code Injection - exec",
			code:           "exec('import sys; print(sys.version)')",
			expectSafe:     false,
			expectedRisk:   executor.RiskCritical,
			expectedTypes:  []string{"code_injection"},
			description:    "exec() should be blocked",
		},
		{
			name:           "Dynamic Import",
			code:           "__import__('math').sqrt(16)",
			expectSafe:     false,
			expectedRisk:   executor.RiskHigh,
			expectedTypes:  []string{"dynamic_import"},
			description:    "__import__ should be flagged as high risk",
		},
		{
			name:           "File Access",
			code:           "with open('test.txt', 'w') as f:\n    f.write('test')",
			expectSafe:     true,
			expectedRisk:   executor.RiskMedium,
			expectedTypes:  []string{"file_access"},
			description:    "File access should be flagged but allowed",
		},
		{
			name:           "Infinite Loop Pattern",
			code:           "while True:\n    pass",
			expectSafe:     true,
			expectedRisk:   executor.RiskMedium,
			expectedTypes:  []string{"infinite_loop_pattern"},
			description:    "while True should be flagged as medium risk",
		},
		{
			name:           "Multiple Violations",
			code:           "import os\neval('print(\"test\")')\nopen('file.txt')",
			expectSafe:     false,
			expectedRisk:   executor.RiskCritical,
			expectedTypes:  []string{"blocked_import", "code_injection", "file_access"},
			description:    "Multiple security issues should be detected",
		},
		{
			name:           "Complex Safe Code",
			code:           "import math\nimport json\n\ndef calculate(x):\n    return math.sqrt(x ** 2 + 1)\n\ndata = {'result': calculate(3)}\nprint(json.dumps(data))",
			expectSafe:     true,
			expectedRisk:   executor.RiskSafe,
			expectedTypes:  []string{},
			description:    "Complex but safe code should pass validation",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := validator.ValidateCode(test.code)

			// Check safety expectation
			if result.Safe != test.expectSafe {
				t.Errorf("%s: Expected safe=%v, got safe=%v", test.description, test.expectSafe, result.Safe)
			}

			// Check risk level
			if result.Risk != test.expectedRisk {
				t.Errorf("%s: Expected risk=%v (%s), got risk=%v (%s)",
					test.description, test.expectedRisk, test.expectedRisk.GetRiskDescription(),
					result.Risk, result.Risk.GetRiskDescription())
			}

			// Check that expected violation types are present
			foundTypes := make(map[string]bool)
			for _, violation := range result.Violations {
				foundTypes[violation.Type] = true
			}

			for _, expectedType := range test.expectedTypes {
				if !foundTypes[expectedType] {
					t.Errorf("%s: Expected violation type '%s' not found. Found types: %v",
						test.description, expectedType, getViolationTypes(result.Violations))
				}
			}

			// Log results for debugging
			if len(result.Violations) > 0 {
				t.Logf("%s: Found %d violations: %v", test.name, len(result.Violations), getViolationTypes(result.Violations))
				for _, v := range result.Violations {
					t.Logf("  - %s: %s (Risk: %s, Line: %d)", v.Type, v.Description, v.Risk.GetRiskDescription(), v.Line)
				}
			} else {
				t.Logf("%s: No violations found (safe code)", test.name)
			}
		})
	}
}

// TestASTValidatorObfuscationDetection tests detection of obfuscated malicious code
func TestASTValidatorObfuscationDetection(t *testing.T) {
	cfg := createTestSecurityConfig()
	validator := executor.NewCodeValidator(cfg)

	tests := []struct {
		name         string
		code         string
		expectSafe   bool
		description  string
	}{
		{
			name: "Base64 Obfuscation",
			code: `
import base64
code = base64.b64decode('cHJpbnQoImhpZGRlbiBjb2RlIik=').decode()
exec(code)`,
			expectSafe:  false,
			description: "Base64 obfuscated exec should be detected",
		},
		{
			name: "String Concatenation Obfuscation",
			code: `
func_name = 'e' + 'v' + 'a' + 'l'
getattr(__builtins__, func_name)('print("obfuscated")')`,
			expectSafe:  false,
			description: "String concatenation to build dangerous function names",
		},
		{
			name: "Hex Encoding",
			code: `
import codecs
code = codecs.decode('7072696E7428226865782065786563757465642229', 'hex').decode()
exec(code)`,
			expectSafe:  false,
			description: "Hex encoded code execution should be detected",
		},
		{
			name: "Multi-line Statement Splitting",
			code: `
e = eval
dangerous_code = 'import os; os.system("echo pwned")'
e(dangerous_code)`,
			expectSafe:  false,
			description: "Multi-line eval usage should be detected",
		},
		{
			name: "Attribute Access Obfuscation",
			code: `
import sys
getattr(sys, 'exit')(1)`,
			expectSafe:  false,
			description: "Dangerous attribute access should be detected",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := validator.ValidateCode(test.code)

			if result.Safe != test.expectSafe {
				t.Errorf("%s: Expected safe=%v, got safe=%v. Violations: %v",
					test.description, test.expectSafe, result.Safe, getViolationTypes(result.Violations))
			}

			if !test.expectSafe && len(result.Violations) == 0 {
				t.Errorf("%s: Expected violations but none found", test.description)
			}

			t.Logf("%s: Risk=%s, Violations=%d", test.name, result.Risk.GetRiskDescription(), len(result.Violations))
		})
	}
}

// TestASTValidatorConfigurationChanges tests runtime configuration updates
func TestASTValidatorConfigurationChanges(t *testing.T) {
	// Start with restrictive config
	restrictiveConfig := &config.SecurityConfig{
		BlockedImports:    []string{"os", "sys", "json"},
		RestrictedImports: []string{"math"},
		Validation: config.ValidationConfig{
			EnableAST:         true,
			MaxComplexity:     5,
			MaxRecursionDepth: 50,
		},
		WhitelistMode: false,
	}

	validator := executor.NewCodeValidator(restrictiveConfig)
	testCode := "import json\nprint(json.dumps({'test': 'value'}))"

	// Test with restrictive config
	result1 := validator.ValidateCode(testCode)
	if result1.Safe {
		t.Error("Expected code to be blocked with restrictive config")
	}

	// Update to permissive config
	permissiveConfig := &config.SecurityConfig{
		BlockedImports:    []string{"os", "sys"},
		RestrictedImports: []string{"numpy"},
		Validation: config.ValidationConfig{
			EnableAST:         true,
			MaxComplexity:     20,
			MaxRecursionDepth: 200,
		},
		WhitelistMode: false,
	}

	validator.UpdateConfig(permissiveConfig)

	// Test with permissive config
	result2 := validator.ValidateCode(testCode)
	if !result2.Safe {
		t.Errorf("Expected code to be safe with permissive config, but got violations: %v",
			getViolationTypes(result2.Violations))
	}

	t.Logf("Restrictive config: Safe=%v, Violations=%d", result1.Safe, len(result1.Violations))
	t.Logf("Permissive config: Safe=%v, Violations=%d", result2.Safe, len(result2.Violations))
}

// TestASTValidatorWhitelistMode tests whitelist functionality
func TestASTValidatorWhitelistMode(t *testing.T) {
	whitelistConfig := &config.SecurityConfig{
		BlockedImports:    []string{},
		RestrictedImports: []string{},
		AllowedImports:    []string{"math", "json", "datetime"},
		Validation: config.ValidationConfig{
			EnableAST: true,
		},
		WhitelistMode: true,
	}

	validator := executor.NewCodeValidator(whitelistConfig)

	tests := []struct {
		name       string
		code       string
		expectSafe bool
	}{
		{
			name:       "Allowed Import",
			code:       "import math\nprint(math.sqrt(16))",
			expectSafe: true,
		},
		{
			name:       "Disallowed Import",
			code:       "import os\nprint('test')",
			expectSafe: false,
		},
		{
			name:       "Multiple Allowed Imports",
			code:       "import math\nimport json\nprint(json.dumps({'sqrt': math.sqrt(16)}))",
			expectSafe: true,
		},
		{
			name:       "Mixed Allowed/Disallowed",
			code:       "import math\nimport sys\nprint(math.sqrt(16))",
			expectSafe: false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := validator.ValidateCode(test.code)

			if result.Safe != test.expectSafe {
				t.Errorf("Expected safe=%v, got safe=%v. Violations: %v",
					test.expectSafe, result.Safe, getViolationTypes(result.Violations))
			}
		})
	}
}

// TestASTValidatorPerformance tests validation performance
func TestASTValidatorPerformance(t *testing.T) {
	cfg := createTestSecurityConfig()
	validator := executor.NewCodeValidator(cfg)

	testCodes := []string{
		"print('Hello World')",
		"import math\nresult = math.sqrt(16)\nprint(result)",
		`
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

print(fibonacci(10))
`,
		`
import json
data = {"numbers": [i**2 for i in range(100)]}
json_str = json.dumps(data)
parsed = json.loads(json_str)
print(len(parsed["numbers"]))
`,
		`
class Calculator:
    def __init__(self):
        self.history = []
    
    def add(self, a, b):
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def get_history(self):
        return self.history

calc = Calculator()
print(calc.add(5, 3))
print(calc.get_history())
`,
	}

	for i, code := range testCodes {
		start := time.Now()
		result := validator.ValidateCode(code)
		duration := time.Since(start)

		t.Logf("Code %d: Duration=%v, Risk=%s, Violations=%d, AnalysisType=%s",
			i+1, duration, result.Risk.GetRiskDescription(), len(result.Violations), result.AnalysisType)

		// AST validation should be reasonably fast
		maxDuration := 2 * time.Second // Allow more time for Python subprocess
		if duration > maxDuration {
			t.Errorf("Code %d validation took too long: %v (max: %v)", i+1, duration, maxDuration)
		}

		// Verify analysis type
		expectedType := "ast_based"
		if result.AnalysisType != expectedType {
			t.Errorf("Code %d: Expected analysis type '%s', got '%s'",
				i+1, expectedType, result.AnalysisType)
		}
	}
}

}

// TestASTValidatorIntegration tests integration with executor
func TestASTValidatorIntegration(t *testing.T) {
	cfg := &config.Config{
		AuthToken:     "test-token",
		ServerPort:    "8080",
		WorkerCount:   1,
		QueueSize:     10,
		ExecTimeout:   30 * time.Second,
		MemoryLimitMB: 256,
		MaxCodeSize:   1024 * 1024,
		RateLimitRPS:  10,
		SecurityConfig: *createTestSecurityConfig(),
	}

	executor := executor.NewExecutor(cfg)

	tests := []struct {
		name          string
		code          string
		expectSuccess bool
		description   string
	}{
		{
			name:          "Safe Code Execution",
			code:          "print('Integration test successful')",
			expectSuccess: true,
			description:   "Safe code should execute successfully",
		},
		{
			name:          "Blocked Code Execution",
			code:          "import os; os.system('echo blocked')",
			expectSuccess: false,
			description:   "Dangerous code should be blocked",
		},
		{
			name:          "Restricted Code Execution",
			code:          "import numpy as np; print(np.array([1,2,3]))",
			expectSuccess: true,
			description:   "Restricted imports should execute with warnings",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			job := executor.Job{
				ID:   "integration-test-" + test.name,
				Code: test.code,
			}

			result := executor.Execute(context.Background(), job)

			if result.Success != test.expectSuccess {
				t.Errorf("%s: Expected success=%v, got success=%v. Error: %s",
					test.description, test.expectSuccess, result.Success, result.Error)
			}

			if test.expectSuccess && result.Success {
				t.Logf("✓ %s: Code executed successfully", test.name)
			} else if !test.expectSuccess && !result.Success {
				t.Logf("✓ %s: Code properly blocked - %s", test.name, result.Error)
			}
		})
	}
}

// Helper Functions

func createTestSecurityConfig() *config.SecurityConfig {
	return &config.SecurityConfig{
		BlockedImports: []string{
			"os", "sys", "subprocess", "socket", "urllib",
			"ctypes", "multiprocessing", "threading",
			"importlib", "__builtin__", "builtins", "marshal",
		},
		RestrictedImports: []string{
			"numpy", "pandas", "matplotlib", "scipy",
		},
		AllowedImports: []string{},
		Validation: config.ValidationConfig{
			EnableAST:         true,
			MaxComplexity:     10,
			MaxRecursionDepth: 100,
		},
		WhitelistMode: false,
	}
}

func getViolationTypes(violations []executor.SecurityViolation) []string {
	types := make([]string, len(violations))
	for i, v := range violations {
		types[i] = v.Type
	}
	return types
}